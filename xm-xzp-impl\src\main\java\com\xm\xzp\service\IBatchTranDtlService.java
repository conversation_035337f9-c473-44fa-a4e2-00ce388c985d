package com.xm.xzp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.model.entity.BatchTranDtl;
import com.xm.xzp.model.vo.BatchTranDtlDisplayVo;
import com.xm.xzp.model.vo.BatchTranDtlVo;

public interface IBatchTranDtlService extends IService<BatchTranDtl> {
    PageInfo<BatchTranDtlDisplayVo> batchTranDtlList(BatchTranDtlVo batchTranDtlVo, Integer pageNum, Integer pageSize);
}
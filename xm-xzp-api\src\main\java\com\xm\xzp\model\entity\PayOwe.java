package com.xm.xzp.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("tb_pay_owe")
@ApiModel(description = "欠费记录表")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class PayOwe {

    @ApiModelProperty("加载日期")
    @TableField("load_dt")
    private String loadDt;

    @ApiModelProperty("序列号")
    @TableField("seq_sq")
    private String seqSq;

    @ApiModelProperty("操作码")
    @TableField("ope_cd")
    private String opeCd;

    @ApiModelProperty("商户号")
    @TableField("merch_id")
    private String merchId;

    @ApiModelProperty("支付ID")
    @TableField("pay_id")
    private String payId;

    @ApiModelProperty("服务代码")
    @TableField("sp_code")
    private String spCode;

    @ApiModelProperty("用户ID")
    @TableField("user_id")
    private String userId;

    @ApiModelProperty("账户卡号")
    @TableField("acc_card_id")
    private String accCardId;

    @ApiModelProperty("账户日期")
    @TableField("account_dt")
    private String accountDt;

    @ApiModelProperty("子键代码")
    @TableField("sub_key_cd")
    private String subKeyCd;

    @ApiModelProperty("违约日期")
    @TableField("break_dt")
    private String breakDt;

    @ApiModelProperty("违约率")
    @TableField("break_rate")
    private BigDecimal breakRate;

    @ApiModelProperty("欠费金额")
    @TableField("owe_at")
    private BigDecimal oweAt;

    @ApiModelProperty("延迟金额")
    @TableField("delay_at")
    private BigDecimal delayAt;

    @ApiModelProperty("费用金额")
    @TableField("fee_at")
    private BigDecimal feeAt;

    @ApiModelProperty("交易金额")
    @TableField("tran_at")
    private BigDecimal tranAt;

    @ApiModelProperty("金额1")
    @TableField("amt1")
    private BigDecimal amt1;

    @ApiModelProperty("金额2")
    @TableField("amt2")
    private BigDecimal amt2;

    @ApiModelProperty("交易日期")
    @TableField("tran_dt")
    private String tranDt;

    @ApiModelProperty("批次号")
    @TableField("wl_batch_id")
    private String wlBatchId;

    @ApiModelProperty("批次ID")
    @TableField("batch_id")
    private String batchId;

    @ApiModelProperty("明细序列ID")
    @TableField("dtl_seq_id")
    private String dtlSeqId;

    @ApiModelProperty("客户名称")
    @TableField("cust_nm")
    private String custNm;

    @ApiModelProperty("交易机构ID")
    @TableField("tran_inst_id")
    private String tranInstId;

    @ApiModelProperty("交易序列号")
    @TableField("tran_sq")
    private String tranSq;

    @ApiModelProperty("统一清算日期")
    @TableField("unite_clr_dt")
    private String uniteClrDt;

    @ApiModelProperty("账户类型")
    @TableField("acc_type")
    private String accType;

    @ApiModelProperty("卡片标志")
    @TableField("card_pk_fg")
    private String cardPkFg;

    @ApiModelProperty("卡号")
    @TableField("card_id")
    private String cardId;

    @ApiModelProperty("账户名称")
    @TableField("acc_nm")
    private String accNm;

    @ApiModelProperty("账户余额")
    @TableField("acc_bal")
    private BigDecimal accBal;

    @ApiModelProperty("交易状态")
    @TableField("txn_sta")
    private String txnSta;

    @ApiModelProperty("费用有效性")
    @TableField("fee_vaild")
    private String feeVaild;

    @ApiModelProperty("收据状态")
    @TableField("rcp_sta")
    private Integer rcpSta;

    @ApiModelProperty("收据有效性")
    @TableField("rcp_vaild")
    private String rcpVaild;

    @ApiModelProperty("在线状态")
    @TableField("online_sta")
    private String onlineSta;

    @ApiModelProperty("主机响应代码")
    @TableField("host_rsp_code")
    private String hostRspCode;

    @ApiModelProperty("发票内容")
    @TableField("inv_contents")
    private String invContents;

    @ApiModelProperty("发票内容")
    @TableField("inv_content")
    private String invContent;

    @ApiModelProperty("备注1")
    @TableField("mark1")
    private String mark1;

    @ApiModelProperty("备注2")
    @TableField("mark2")
    private String mark2;

    @ApiModelProperty("备注3")
    @TableField("mark3")
    private String mark3;

    @ApiModelProperty("摘要")
    @TableField("summary")
    private String summary;

    @ApiModelProperty("请求文件")
    @TableField("req_file")
    private String reqFile;

    @ApiModelProperty("柜员ID")
    @TableField("tlr_id")
    private String tlrId;

    @ApiModelProperty("审核员ID")
    @TableField("checker_id")
    private String checkerId;

    @ApiModelProperty("交易代码")
    @TableField("tran_cd")
    private String tranCd;

    @ApiModelProperty("时间戳")
    @TableField("time_stamp")
    private Date timeStamp;
}
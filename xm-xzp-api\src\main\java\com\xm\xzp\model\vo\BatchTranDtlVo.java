package com.xm.xzp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@ApiModel(value = "批量交易明细查询参数")
public class BatchTranDtlVo {

    @ApiModelProperty(value = "商户号")
    private String merchId;

    @ApiModelProperty(value = "操作码")
    private String opeCd;

    @ApiModelProperty(value = "批次号")
    private String batchId;

    @ApiModelProperty(value = "账号/卡号")
    private String accCardId;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = "yyyyMMdd")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = "yyyyMMdd")
    private String endTime;
}
package com.xm.xzp.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "欠费记录查询对象")
public class PayOweVo {
    @ApiModelProperty(value = "商户号")
    private String merchId;
    
    @ApiModelProperty(value = "操作码")
    private String opeCd;
    
    @ApiModelProperty(value = "开始日期")
    private String startTime;
    
    @ApiModelProperty(value = "结束日期")
    private String endTime;
}
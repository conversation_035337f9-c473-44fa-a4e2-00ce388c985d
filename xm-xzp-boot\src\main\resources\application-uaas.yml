############################## 开发环境配置 ##############################
# 项目版本号配置
application:
  version: 1.1.0

# 服务端口号
server:
  port: 9089

# Spring框架相关配置
spring:
  application:
    name: yoaf-server
  # 数据源配置
  datasource:
    url: *****************************************************************
    driver-class-name: org.postgresql.Driver
    username: userapp
    password: userapp
  # 缓存配置
  cache:
    type: redis
  # redis相关配置
  redis:
    database: 3
    host: **************
    port: 6379
    password: 123456
    # 连接超时时间（毫秒）默认是2000ms
    timeout: 2000ms
    # lettuce连接池配置（如不配置则不开启连接池）
    lettuce:
      pool:
        # 连接池大连接数（使用负值表示没有限制）
        max-active: 8
        # 连接池中的大空闲连接
        max-idle: 8
        # 连接池中的小空闲连接
        min-idle: 1
        # 连接池大阻塞等待时间（使用负值表示没有限制）
        max-wait: 1000
        # 关闭超时时间
        shutdown-timeout: 100

# 控制台打印sql
mybatis-plus:
  configuration:
    # SQL日志打印实现类:无日志打印-org.apache.ibatis.logging.nologging.NoLoggingImpl
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# JAVA开发平台配置
pfpj:
  # 统一认证组件配置
  unified-auth:
    # 统一认证组件核心配置
    core:
      # 是否启用统一认证组件(默认不生效)
      enabled: true
      # esb连接方式 centralized/distributed
      esb-model: distributed
      # ESB服务地址
      esb-urls: http://20.198.99.130:7610
      # 业务系统部署的数据中心代码,F-丰台数据中心，Y-亦庄数据中心，H-合肥数据中心
      data-center-code: F
      # 网络环境 01-开发测试网 02-生产办公网 03-生产核心网 04-互联网
      net-env: 01
      # 服务ID
      service-ids: 127.0.0.1-31001|127.0.0.1-31002
      # 【集中式】业务系统ID
      system-no: 99999980000
      # 客户端密钥
      client-secret: 7f0c02fd40976eab42833a3e5e0bc8f88c5429a1843a21d0f0ba288052285100
      # 客户端类型
      client-type: 0
      # Mac鉴别码生成算法类型
      mac-algo-type: 5
      # 是否开启mac码生成(默认不生效)
      mac-generate-enabled: true
    # 统一认证组件令牌相关配置
    token:
      # 是否开启令牌缓存(默认生效)
      cache-enabled: false
      # 是否开启令牌缓存校验(默认生效)
      validate-cache-enabled: false
  # SDK中esb组件配置
  esb:
    # esb是否启用
    enabled: false
    # esb接口地址 测试环境
    business-url-map:
      sms: http://20.200.21.68:8013/direct/TMSSendShortMessage
  # yoaf自定义配置
  yoaf:
    # yoaf缓存模块配置
    cache:
      # yoaf自定义Redis配置
      redis:
        # 缓存配置列表(开发坏境建议)
        other-cache-configs:
          # 手机验证码缓存(默认3分钟)
          - cache-name: yoaf:auth-center:sms-captcha
            ttl: 180
          # 图片验证码缓存(默认5分钟)
          - cache-name: yoaf:auth-center:image-captcha
            ttl: 300
          # 在线用户信息(默认1小时)
          - cache-name: yoaf:auth-center:online-user
            ttl: 1800
          # 黑白名单缓存(默认1小时)
          - cache-name: yoaf:auth-center:black-white
            ttl: 3600
          # 业务字典缓存(默认1小时)
          - cache-name: yoaf:admin:dict-entry
            ttl: 3600
          # 审计日志配置缓存(默认1小时)
          - cache-name: yoaf:auditlog:auditlog-config
            ttl: 3600
          # 功能权限信息缓存(默认1小时)
          - cache-name: yoaf:auth-center:function
            ttl: 3600
          # 角色资源关联信息缓存(默认1小时)
          - cache-name: yoaf:auth-center:res-role
            ttl: 3600
          # 本地授权信息缓存(默认1小时)
          - cache-name: yoaf:auth-center:local-auth
            ttl: 3600
    # 用户信息获取
    user-context:
      # 解析用户信息模式:yoaf(默认),uaas(统一认证)
      user-parse-model: uaas
    # yoaf审计日志配置
    auditlog:
      # 审计日志配置策略(no-cache:无缓存;in-memory:默认内存;spring-cache:Spring缓存)
      auditlog-config-policy: spring-cache
    # yoaf权限认证中心相关配置
    auth:
      core:
        # 是否开启YOAF的黑白名单过滤器
        black-white-enabled: false
        # 是否开启YOAF的本地授权过滤器
        local-auth-enabled: false
        # 忽略资源地址集合(以,分隔)
        ignoring-ant-patterns: /,/csrf,/error,/*.ico,/webjars/**,/swagger-resources/**,/v2/api-docs,/**.html,/api/auth/center/login,/api/auth/center/captcha/**,/api/auth/center/login/public/key,/api/auth/center/actions/refresh,/api/auth/center/uaas/captcha,/api/auth/center/uaas/token/actions/login,/api/auth/center/uaas/portal-url,/api/auth/center/auth/mode,/actuator/health,/actuator/info
#        ignoring-ant-patterns: /**
      # 登录参数配置
      login:
        # 登录核心配置
        core:
          # 令牌策略(stateless:默认无状态且多端登录, store:存储但限一端登录, storeMultiple:存储且多端登录)
          token-policy: storeMultiple
          # 图片验证码
          image-captcha: false
          # 自定义登录地址
          login-url: /api/auth/center/login
          # 自定义登出地址
          logout-url: /api/auth/center/logout
    # swagger配置
    swagger:
      # 是否支持提交请求-默认关闭（关闭之后用户只能查看接口api不能发送请求）
      submit-enabled: true
      # 是否将actuator开发端点地址加入到swagger中-默认关闭（加入swagger中方便安全访问）
      actuator-enabled: true
      # apiKey配置（可配置请求头或请求参数）
      apiKeys:
        # 授权KEY描述
        - name: 普通授权访问令牌
          # 授权KEY名称
          key-name: Authorization
          # 授权KEY类型
          pass-as: header
        # 本地授权KEY描述
        - name: 本地授权用户名
          # 本地授权用户名key
          key-name: Local-Auth-Username
          # 本地授权用户名header
          pass-as: header
        - name: 本地授权用户密码
          # 本地授权用户密码key
          key-name: Local-Auth-Password
          # 本地授权用户header
          pass-as: header
# 设置actuator开放端口(默认全部)
management:
  endpoints:
    web:
      exposure:
        include: '*'
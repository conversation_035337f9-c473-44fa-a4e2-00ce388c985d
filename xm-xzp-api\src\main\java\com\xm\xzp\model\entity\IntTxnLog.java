package com.xm.xzp.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@TableName("tb_int_txn_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "IntTxnLog对象", description = "内部交易日志表")
public class IntTxnLog {
    private String tranDt;
    private String tranSq;
    private String localDt;
    private String nodeType;
    private String tranInstId;
    private String soleFrontSq;
    private String localTm;
    private String iniTranInstId;
    private String tranCd;
    private BigDecimal chnlCd;
    private String instTermId;
    private String outSysCode;
    private String uniteClrDt;
    private String accSerialSq;
    private String merchId;
    private String opeCd;
    private String subOpeCd;
    private String payTypeCd;
    private String payId;
    private String paymentWayCd;
    private String execDt;
    private String checkId;
    private String boxId;
    private String opInstId;
    private String accCardId;
    private BigDecimal tranAt;
    private String merchClrDt;
    private String rspCd;
    private String payCd;
    private String dzCd;
    private String clientdzCd;
    private String invoiceNum;
    private String invoiceHead;
    private String invoiceBodey;
    private String tranStatCd;
}
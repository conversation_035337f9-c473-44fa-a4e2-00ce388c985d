package com.xm.xzp.api;


import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.entity.BatchTranCtrl;
import com.xm.xzp.model.entity.XzpOpLog;
import com.xm.xzp.model.vo.BatchTranCtrlVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 */
@ResponseBody
@RequestMapping("/api/admin/xzp")
@Api(tags = "批量交易查询")
@Validated
public interface BatchTranCtrlApi {
  /**
     * 日志查询
     *
     * @param pageNum    当前页
     * @param batchTranCtrl 接收数据
     * @param pageSize   每页数量
     * @return 数据列表
     */
    @ApiOperation(value = "批量交易列表", notes = "batchTranCtrlList")
    @PostMapping("/batchTranCtrlList")
    RestResponse<PageInfo<BatchTranCtrl>> batchTranCtrlList(@RequestBody BatchTranCtrlVo batchTranCtrl, @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum, @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize);

    @ApiOperation(value = "编辑批量交易", notes = "editBatchTranCtrl")
    @PostMapping("/batchTranCtrlEdit")
    RestResponse<String> editBatchTranCtrl(@RequestBody BatchTranCtrl batchTranCtrl);
}

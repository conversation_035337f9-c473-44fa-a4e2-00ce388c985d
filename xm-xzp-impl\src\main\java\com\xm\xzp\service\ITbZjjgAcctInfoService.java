package com.xm.xzp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.model.entity.TbZjjgAcctInfo;
import com.xm.xzp.model.vo.AcctInfoQueryVo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 资金监管账户信息服务接口
 * <AUTHOR>
 */
@Component
public interface ITbZjjgAcctInfoService extends IService<TbZjjgAcctInfo> {

    /**
     * 根据关键字模糊查询账户信息（用于联想下拉控件）
     * @param keyword 关键字，匹配户名或公司账户
     * @return 账户信息列表
     */
    List<TbZjjgAcctInfo> suggestAcctInfo(String keyword);

    /**
     * 分页查询账户信息
     * @param queryVo 查询条件
     * @param pageNum 当前页
     * @param pageSize 每页数量
     * @return 分页账户信息列表
     */
    PageInfo<TbZjjgAcctInfo> queryAcctInfoPage(AcctInfoQueryVo queryVo, Integer pageNum, Integer pageSize);

    /**
     * 新增账户信息
     * @param acctInfo 账户信息
     * @return 是否成功
     */
    boolean addAcctInfo(TbZjjgAcctInfo acctInfo);
}
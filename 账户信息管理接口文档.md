# 账户信息管理接口文档

## 接口概述

本文档描述了资金监管账户信息管理相关的API接口，包括联想查询、分页查询和新增功能。

## 接口列表

### 1. 账户信息联想查询

**接口路径**: `/api/admin/xzp/suggestAcctInfo`  
**请求方式**: `GET`  
**功能描述**: 根据户名或公司账户模糊查询账户信息列表，主要用于前端下拉控件的联想功能

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | String | 否 | 查询关键字，支持户名或公司账户模糊匹配，为空时返回全量数据 |

#### 响应示例

```json
{
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "merchId": "001",
      "opeCd": "ZZG001",
      "transType": "zzg",
      "cpabAccId": "1234567890123456",
      "openBrhId": "001001",
      "brhName": "厦门分行",
      "pbcBrhId": "102100000001",
      "acctNm": "张三",
      "tranDt": "20241225",
      "paperType": "01",
      "paperId": "350100199001011234",
      "peerAccId": "9876543210987654",
      "dtlCstmNm": "李四",
      "acctType": "1",
      "txnBrhId": "001001001",
      "tranTime": "20241225143000",
      "tlrId": "001"
    }
  ],
  "success": true
}
```

### 2. 账户信息分页查询

**接口路径**: `/api/admin/xzp/queryAcctInfoList`  
**请求方式**: `POST`  
**功能描述**: 根据多个条件分页查询账户信息列表

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 当前页，默认1 |
| pageSize | Integer | 否 | 每页数量，默认10 |

#### 请求体参数 (AcctInfoQueryVo)

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| opeCd | String | 否 | 业务代码 |
| merchId | String | 否 | 委托单位代码 |
| openBrhId | String | 否 | 开户机构 |
| pbcBrhId | String | 否 | 人行机构代码 |
| cpabAccId | String | 否 | 监管账号 |
| acctNm | String | 否 | 账户名称 |
| peerAccId | String | 否 | 本行目标账号 |
| dtlCstmNm | String | 否 | 本行目标账号名称 |

#### 请求示例

```json
{
  "opeCd": "ZZG001",
  "merchId": "001",
  "acctNm": "张三"
}
```

#### 响应示例

```json
{
  "code": "200",
  "message": "操作成功",
  "data": {
    "total": 100,
    "list": [
      {
        "merchId": "001",
        "opeCd": "ZZG001",
        "transType": "zzg",
        "cpabAccId": "1234567890123456",
        "acctNm": "张三",
        "openBrhId": "001001",
        "brhName": "厦门分行"
      }
    ],
    "pageNum": 1,
    "pageSize": 10,
    "pages": 10
  },
  "success": true
}
```

### 3. 新增账户信息

**接口路径**: `/api/admin/xzp/addAcctInfo`  
**请求方式**: `POST`  
**功能描述**: 新增资金监管账户信息

#### 请求体参数 (TbZjjgAcctInfo)

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| merchId | String | 是 | 委托单位代码，最大32字符 |
| opeCd | String | 是 | 业务代码，最大32字符 |
| cpabAccId | String | 是 | 公司账户，最大32字符 |
| acctNm | String | 是 | 户名，最大128字符 |
| transType | String | 否 | 业务类型，默认"zzg"，最大16字符 |
| openBrhId | String | 否 | 开户机构号，最大32字符 |
| brhName | String | 否 | 开户机构名称，最大128字符 |
| pbcBrhId | String | 否 | 人行机构号，最大32字符 |
| tranDt | String | 否 | 交易日期，格式YYYYMMDD，最大8字符 |
| paperType | String | 否 | 证件类型，最大8字符 |
| paperId | String | 否 | 证件号码，最大32字符 |
| peerAccId | String | 否 | 关联账号，最大32字符 |
| dtlCstmNm | String | 否 | 关联户名，最大128字符 |
| acctType | String | 否 | 关联账户类型（1-同行，2-跨行），最大8字符 |
| txnBrhId | String | 否 | 交易机构，最大32字符 |
| tranTime | String | 否 | 交易时间戳，格式YYYYMMDDHHMMSS，最大14字符 |
| tlrId | String | 否 | 操作员，最大16字符 |
| othMsg1Tx | String | 否 | 附加字段1，最大255字符 |
| othMsg2Tx | String | 否 | 附加字段2，最大255字符 |
| othMsg3Tx | String | 否 | 附加字段3，最大255字符 |
| othMsg4Tx | String | 否 | 附加字段4，最大255字符 |
| othMsg5Tx | String | 否 | 附加字段5，最大255字符 |

#### 请求示例

```json
{
  "merchId": "001",
  "opeCd": "ZZG001",
  "cpabAccId": "1234567890123456",
  "acctNm": "张三",
  "openBrhId": "001001",
  "brhName": "厦门分行",
  "pbcBrhId": "102100000001",
  "paperType": "01",
  "paperId": "350100199001011234",
  "peerAccId": "9876543210987654",
  "dtlCstmNm": "李四",
  "acctType": "1",
  "txnBrhId": "001001001",
  "tlrId": "001"
}
```

#### 响应示例

```json
{
  "code": "200",
  "message": "操作成功",
  "data": "新增账户信息成功",
  "success": true
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有接口都需要进行身份认证
2. 新增接口会自动检查重复数据（根据公司账户和户名判断）
3. 如果未设置交易日期和交易时间戳，系统会自动设置为当前时间
4. 业务类型默认为"zzg"（资金监管）
5. 分页查询支持多条件组合查询，所有条件都是可选的
6. 联想查询在有关键字时最多返回100条记录，无关键字时返回全量数据

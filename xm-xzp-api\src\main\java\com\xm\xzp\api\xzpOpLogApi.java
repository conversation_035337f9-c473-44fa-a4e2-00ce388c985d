package com.xm.xzp.api;


import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.entity.XzpOpLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 */
@ResponseBody
@RequestMapping("/api/admin/xzp/log")
@Api(tags = "新中平日志")
@Validated
public interface xzpOpLogApi {
  /**
     * 日志查询
     *
     * @param pageNum    当前页
     * @param xzpOpLog 接收用户数据
     * @param pageSize   每页沙宣数量
     * @return 数据列表
     */
    @ApiOperation(value = "日志列表", notes = "jgzhSpList")
    @PostMapping("/xzpLogList")
    RestResponse<PageInfo<XzpOpLog>> xzpLogList(@RequestBody XzpOpLog xzpOpLog, @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum, @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize);

    @ApiOperation(value = "监管账户被执行信息审核详情", notes = "jgzhSpDetail")
    @GetMapping("/logDetail/{id}")
    RestResponse<XzpOpLog> logDetail(@PathVariable("id") String id);

  @ApiOperation(value = "查询敏感信息", notes = "getLogSensitiveInfo")
  @GetMapping("/sensitive/get/{id}/{sensitiveStatus}")
  RestResponse<XzpOpLog> getLogSensitiveInfo(@PathVariable("id") String id,
                                                @PathVariable("sensitiveStatus") String sensitiveStatus);


}

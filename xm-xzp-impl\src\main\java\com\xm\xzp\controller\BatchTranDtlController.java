package com.xm.xzp.controller;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.api.BatchTranDtlApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.vo.BatchTranDtlDisplayVo;
import com.xm.xzp.model.vo.BatchTranDtlVo;
import com.xm.xzp.service.IBatchTranDtlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
public class BatchTranDtlController implements BatchTranDtlApi {

    @Resource
    private IBatchTranDtlService batchTranDtlService;

    @Override
    @PMCTLLog(name = "查询批量交易明细", action = "query")
    public RestResponse<PageInfo<BatchTranDtlDisplayVo>> batchTranDtlList(
            BatchTranDtlVo batchTranDtlVo, Integer pageNum, Integer pageSize) {
        PageInfo<BatchTranDtlDisplayVo> pageInfo = batchTranDtlService.batchTranDtlList(batchTranDtlVo, pageNum, pageSize);
        return RestResponse.success(pageInfo);
    }
}
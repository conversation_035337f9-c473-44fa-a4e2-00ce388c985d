package com.xm.xzp.api;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * @Description: 营销积分零售信贷业务表
 * @Author: Ye<PERSON>hui
 * @Date: 2024-09-18
 * @Version: V1.0
 */
@Data
@TableName("t_js_yxjf_lsxdye")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "t_js_yxjf_lsxdye对象", description = "新一代综管表")
public class XzpData implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 数据日期
     */
    @ApiModelProperty(value = "数据日期")
    private String summDate;
    /**
     * 贷款申请ID
     */
    @ApiModelProperty(value = "贷款申请ID")
    private String loanApplyId;
    /**
     * 借据编号
     */
    @ApiModelProperty(value = "借据编号")
    private String loanRctNo;
    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    private String contractNo;
    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID")
    private String custId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String custName;
    /**
     * 贷款种类编号
     */
    @ApiModelProperty(value = "贷款种类编号")
    private String loanKindNo;
    /**
     * 贷款起始日期
     */
    @ApiModelProperty(value = "贷款起始日期")
    private String loanRctBgnDate;
    /**
     * 贷款终止日期
     */
    @ApiModelProperty(value = "贷款终止日期")
    private String loanRctEndDate;
    /**
     * 贷款出账金额
     */
    @ApiModelProperty(value = "贷款出账金额")
    private BigDecimal loanOutaccAmt;
    /**
     * 贷款合同金额
     */
    @ApiModelProperty(value = "贷款合同金额")
    private BigDecimal loanContractAmt;
    /**
     * 贷款利率
     */
    @ApiModelProperty(value = "贷款利率")
    private String loanIntRate;
    /**
     * 利率调整方式
     */
    @ApiModelProperty(value = "利率调整方式")
    private String intRateAdjMode;
    /**
     * 贷款余额（余额为（贷款余额+减值损失余额））
     */
    @ApiModelProperty(value = "贷款余额（余额为（贷款余额+减值损失余额））")
    private String loanBal;
    /**
     * 减值损失余额
     */
    @ApiModelProperty(value = "减值损失余额")
    private String devalLossBal;
    /**
     * 销户日期
     */
    @ApiModelProperty(value = "销户日期")
    private String clsAccDate;
    /**
     * 还款方式
     */
    @ApiModelProperty(value = "还款方式")
    private String repayMode;
    /**
     * 主担保方式（1-信用；2-保证；3-抵押；4-质押；9-其他）
     */
    @ApiModelProperty(value = "主担保方式（1-信用；2-保证；3-抵押；4-质押；9-其他）")
    private String mainGuaMode;
    /**
     * 经办机构号
     */
    @ApiModelProperty(value = "经办机构号")
    private String busiBelongInstNo;
    /**
     * 业务管户人ID
     */
    @ApiModelProperty(value = "业务管户人ID")
    private String busiAccMngPersId;
    /**
     * 业务管户人名称
     */
    @ApiModelProperty(value = "业务管户人名称")
    private String busiAccMngPersName;
    /**
     * 贷款状态代码
     */
    @ApiModelProperty(value = "贷款状态代码")
    private String loanStatusCode;
    /**
     * 额度合同编号
     */
    @ApiModelProperty(value = "额度合同编号")
    private String limitContractNo;
    /**
     * 结清日期
     */
    @ApiModelProperty(value = "结清日期")
    private String payOffDate;
    /**
     * 是否农户标志
     */
    @ApiModelProperty(value = "是否农户标志")
    private String farmFlag;
    /**
     * 客户行业类型代码
     */
    @ApiModelProperty(value = "客户行业类型代码")
    private String indsTypeCode;
    /**
     * 贷款五级分类代码（1-正常;2-关注;3-次级;4-可疑;5-损失）
     */
    @ApiModelProperty(value = "贷款五级分类代码（1-正常;2-关注;3-次级;4-可疑;5-损失）")
    private String loanLvl5ClassCode;
    /**
     * 客户主体类别（06-个体工商户;07-小微企业主;15其他）
     */
    @ApiModelProperty(value = "客户主体类别（06-个体工商户;07-小微企业主;15其他）")
    private String farmOrMerMainbCateg;
    /**
     * 是否涉农贷款
     */
    @ApiModelProperty(value = "是否涉农贷款")
    private String snFlag;
    /**
     * 经办机构名称
     */
    @ApiModelProperty(value = "经办机构名称")
    private String busiBelongInstName;
    /**
     * 币种代码
     */
    @ApiModelProperty(value = "币种代码")
    private String currCode;
    /**
     * 贷款种类编号
     */
    @ApiModelProperty(value = "贷款种类编号")
    private String loanKindName;
    /**
     * 数据来源标识（1-信贷业务平台；2-互联网网贷）
     */
    @ApiModelProperty(value = "数据来源标识（1-信贷业务平台；2-互联网网贷）")
    private String srcFlag;
    /**
     * 作业执行日期
     */
    @ApiModelProperty(value = "作业执行日期")
    private String bdsEtlJobDt;
    /**
     * 放款账户账号
     */
    @ApiModelProperty(value = "放款账户账号")
    private String disburseAccno;
    /**
     * 还款账户账号
     */
    @ApiModelProperty(value = "还款账户账号")
    private String repayAccAccNo;
    /**
     * 利率类型
     */
    @ApiModelProperty(value = "利率类型")
    private String intRateType;
    /**
     * 贴息前执行利率
     */
    @ApiModelProperty(value = "贴息前执行利率")
    private String subsidyBfrExecIntRate;
    /**
     * 居住详细地址
     */
    @ApiModelProperty(value = "居住详细地址")
    private String rsdDtlAddr;
    /**
     * 贷款投向
     */
    @ApiModelProperty(value = "贷款投向")
    private String loanDrctCd;
    /**
     * 证件号码_脱敏
     */
    @ApiModelProperty(value = "证件号码_脱敏")
    private String zjhmTm;
    /**
     * 户籍地址
     */
    @ApiModelProperty(value = "户籍地址")
    private String hsRegAddr;

    private String numb;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getSummDate() {
        return summDate;
    }

    public void setSummDate(String summDate) {
        this.summDate = summDate;
    }

    public String getLoanApplyId() {
        return loanApplyId;
    }

    public void setLoanApplyId(String loanApplyId) {
        this.loanApplyId = loanApplyId;
    }

    public String getLoanRctNo() {
        return loanRctNo;
    }

    public void setLoanRctNo(String loanRctNo) {
        this.loanRctNo = loanRctNo;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getLoanKindNo() {
        return loanKindNo;
    }

    public void setLoanKindNo(String loanKindNo) {
        this.loanKindNo = loanKindNo;
    }

    public String getLoanRctBgnDate() {
        return loanRctBgnDate;
    }

    public void setLoanRctBgnDate(String loanRctBgnDate) {
        this.loanRctBgnDate = loanRctBgnDate;
    }

    public String getLoanRctEndDate() {
        return loanRctEndDate;
    }

    public void setLoanRctEndDate(String loanRctEndDate) {
        this.loanRctEndDate = loanRctEndDate;
    }

    public BigDecimal getLoanOutaccAmt() {
        return loanOutaccAmt;
    }

    public void setLoanOutaccAmt(BigDecimal loanOutaccAmt) {
        this.loanOutaccAmt = loanOutaccAmt;
    }

    public BigDecimal getLoanContractAmt() {
        return loanContractAmt;
    }

    public void setLoanContractAmt(BigDecimal loanContractAmt) {
        this.loanContractAmt = loanContractAmt;
    }

    public String getLoanIntRate() {
        return loanIntRate;
    }

    public void setLoanIntRate(String loanIntRate) {
        this.loanIntRate = loanIntRate;
    }

    public String getIntRateAdjMode() {
        return intRateAdjMode;
    }

    public void setIntRateAdjMode(String intRateAdjMode) {
        this.intRateAdjMode = intRateAdjMode;
    }

    public String getLoanBal() {
        return loanBal;
    }

    public void setLoanBal(String loanBal) {
        this.loanBal = loanBal;
    }

    public String getDevalLossBal() {
        return devalLossBal;
    }

    public void setDevalLossBal(String devalLossBal) {
        this.devalLossBal = devalLossBal;
    }

    public String getClsAccDate() {
        return clsAccDate;
    }

    public void setClsAccDate(String clsAccDate) {
        this.clsAccDate = clsAccDate;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }

    public String getMainGuaMode() {
        return mainGuaMode;
    }

    public void setMainGuaMode(String mainGuaMode) {
        this.mainGuaMode = mainGuaMode;
    }

    public String getBusiBelongInstNo() {
        return busiBelongInstNo;
    }

    public void setBusiBelongInstNo(String busiBelongInstNo) {
        this.busiBelongInstNo = busiBelongInstNo;
    }

    public String getBusiAccMngPersId() {
        return busiAccMngPersId;
    }

    public void setBusiAccMngPersId(String busiAccMngPersId) {
        this.busiAccMngPersId = busiAccMngPersId;
    }

    public String getBusiAccMngPersName() {
        return busiAccMngPersName;
    }

    public void setBusiAccMngPersName(String busiAccMngPersName) {
        this.busiAccMngPersName = busiAccMngPersName;
    }

    public String getLoanStatusCode() {
        return loanStatusCode;
    }

    public void setLoanStatusCode(String loanStatusCode) {
        this.loanStatusCode = loanStatusCode;
    }

    public String getLimitContractNo() {
        return limitContractNo;
    }

    public void setLimitContractNo(String limitContractNo) {
        this.limitContractNo = limitContractNo;
    }

    public String getPayOffDate() {
        return payOffDate;
    }

    public void setPayOffDate(String payOffDate) {
        this.payOffDate = payOffDate;
    }

    public String getFarmFlag() {
        return farmFlag;
    }

    public void setFarmFlag(String farmFlag) {
        this.farmFlag = farmFlag;
    }

    public String getIndsTypeCode() {
        return indsTypeCode;
    }

    public void setIndsTypeCode(String indsTypeCode) {
        this.indsTypeCode = indsTypeCode;
    }

    public String getLoanLvl5ClassCode() {
        return loanLvl5ClassCode;
    }

    public void setLoanLvl5ClassCode(String loanLvl5ClassCode) {
        this.loanLvl5ClassCode = loanLvl5ClassCode;
    }

    public String getFarmOrMerMainbCateg() {
        return farmOrMerMainbCateg;
    }

    public void setFarmOrMerMainbCateg(String farmOrMerMainbCateg) {
        this.farmOrMerMainbCateg = farmOrMerMainbCateg;
    }

    public String getSnFlag() {
        return snFlag;
    }

    public void setSnFlag(String snFlag) {
        this.snFlag = snFlag;
    }

    public String getBusiBelongInstName() {
        return busiBelongInstName;
    }

    public void setBusiBelongInstName(String busiBelongInstName) {
        this.busiBelongInstName = busiBelongInstName;
    }

    public String getCurrCode() {
        return currCode;
    }

    public void setCurrCode(String currCode) {
        this.currCode = currCode;
    }

    public String getLoanKindName() {
        return loanKindName;
    }

    public void setLoanKindName(String loanKindName) {
        this.loanKindName = loanKindName;
    }

    public String getSrcFlag() {
        return srcFlag;
    }

    public void setSrcFlag(String srcFlag) {
        this.srcFlag = srcFlag;
    }

    public String getBdsEtlJobDt() {
        return bdsEtlJobDt;
    }

    public void setBdsEtlJobDt(String bdsEtlJobDt) {
        this.bdsEtlJobDt = bdsEtlJobDt;
    }

    public String getDisburseAccno() {
        return disburseAccno;
    }

    public void setDisburseAccno(String disburseAccno) {
        this.disburseAccno = disburseAccno;
    }

    public String getRepayAccAccNo() {
        return repayAccAccNo;
    }

    public void setRepayAccAccNo(String repayAccAccNo) {
        this.repayAccAccNo = repayAccAccNo;
    }

    public String getIntRateType() {
        return intRateType;
    }

    public void setIntRateType(String intRateType) {
        this.intRateType = intRateType;
    }

    public String getSubsidyBfrExecIntRate() {
        return subsidyBfrExecIntRate;
    }

    public void setSubsidyBfrExecIntRate(String subsidyBfrExecIntRate) {
        this.subsidyBfrExecIntRate = subsidyBfrExecIntRate;
    }

    public String getRsdDtlAddr() {
        return rsdDtlAddr;
    }

    public void setRsdDtlAddr(String rsdDtlAddr) {
        this.rsdDtlAddr = rsdDtlAddr;
    }

    public String getLoanDrctCd() {
        return loanDrctCd;
    }

    public void setLoanDrctCd(String loanDrctCd) {
        this.loanDrctCd = loanDrctCd;
    }

    public String getZjhmTm() {
        return zjhmTm;
    }

    public void setZjhmTm(String zjhmTm) {
        this.zjhmTm = zjhmTm;
    }

    public String getHsRegAddr() {
        return hsRegAddr;
    }

    public void setHsRegAddr(String hsRegAddr) {
        this.hsRegAddr = hsRegAddr;
    }

    public String getNumb() {
        return numb;
    }

    public void setNumb(String numb) {
        this.numb = numb;
    }
}

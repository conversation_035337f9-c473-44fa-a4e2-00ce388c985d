package com.xm.xzp.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xm.xzp.model.entity.JgzhData;
import com.xm.xzp.model.entity.JgzhSpData;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description:
 * @Author: Lyl
 * @Date:   2024-11-04
 * @Version: V1.0
 */
@Component
public interface JgzhSpDataMapper extends BaseMapper<JgzhSpData> {

    List<JgzhSpData> queryList(JgzhSpData jgzhShQueryWrapper);

    boolean insertJgzhSpData(JgzhData jgzhData);

    JgzhSpData selectInfoById(String id);

    int updateBatchSpRes(@Param("spList")List<JgzhSpData> spList);

    int insertJgzhSpDataBatch(@Param("jgzhDataList")List<JgzhData> jgzhDataList);

    JgzhSpData selectOneById(JgzhSpData jgzhSpData);

    int deleteByApplyId(@Param("applyId")String id);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xm.xzp.mapper.JgzhSpDataMapper">
        <resultMap id="jgzhSpInfoMap" type="com.xm.xzp.model.entity.JgzhSpData">
            <result column="id" jdbcType="VARCHAR" property="id"/>
            <result column="sp_res" jdbcType="VARCHAR" property="spRes"/>
            <result column="sp_status" jdbcType="VARCHAR" property="spStatus"/>
            <result column="sp_content" jdbcType="VARCHAR" property="spContent"/>
            <result column="sp_by" jdbcType="VARCHAR" property="spBy"/>
            <result column="sp_time" jdbcType="VARCHAR" property="spTime"/>
            <result column="sp_classification" jdbcType="VARCHAR" property="spClassification"/>
            <result column="jgzh_id" jdbcType="VARCHAR" property="jgzhId"/>
            <result column="serialno" jdbcType="VARCHAR" property="serialno"/>
            <result column="bankid" jdbcType="VARCHAR" property="bankid"/>
            <result column="accountname" jdbcType="VARCHAR" property="accountname"/>
            <result column="accountno" jdbcType="VARCHAR" property="accountno"/>
            <result column="executetype" jdbcType="VARCHAR" property="executetype"/>
            <result column="executeamount" jdbcType="VARCHAR" property="executeamount"/>
            <result column="executedept" jdbcType="VARCHAR" property="executedept"/>
            <result column="executedate" jdbcType="VARCHAR" property="executedate"/>
            <result column="releaseserialno" jdbcType="VARCHAR" property="releaseserialno"/>
            <result column="releasetime" jdbcType="VARCHAR" property="releasetime"/>
            <result column="note" jdbcType="VARCHAR" property="note"/>
            <result column="data_source" jdbcType="VARCHAR" property="dataSource"/>
            <result column="jgzh_status" jdbcType="VARCHAR" property="jgzhStatus"/>
            <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="create_by" jdbcType="TIMESTAMP" property="createBy"/>
            <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
            <result column="update_by" jdbcType="VARCHAR" property="updateby"/>
        </resultMap>

        <!--  分页查询字典类型列表  -->
        <select id="queryList" resultMap="jgzhSpInfoMap">
            select s.id
                ,s.sp_res
                ,coalesce(s.sp_status,'0')  as sp_status
                ,s.sp_content
                ,s.sp_by
                ,s.sp_time
                ,s.sp_classification
                ,s.application_id
                ,j.id as jgzh_id
                ,j.serialno
                ,j.bankid
                ,j.accountname
                ,j.accountno
                ,j.executetype
                ,j.executeamount
                ,j.executedept
                ,j.executedate
                ,j.releaseserialno
                ,j.releasetime
                ,j.note
                ,j.jgzh_status
                ,j.data_source
            from  xzp_sp_info s
            left join xzp_jgzh_info j on s.application_id = j.id
            <where>
                1=1
                <if test="spBy != null and spBy !=''">
                    and s.sp_by like concat('%', #{spBy}, '%')
                </if>
                <if test="spStatus != null and spStatus !=''">
                    <if test = "spStatus=='0'.toString()">
                        and s.sp_status is null or s.sp_status = '0'
                    </if>
                    <if test = "spStatus=='1'.toString()">
                        and s.sp_status = #{spStatus}
                    </if>
                </if>
                <if test="spRes != null and spRes !=''">
                    and s.sp_res = #{spRes}
                </if>
                <if test="serialno != null and serialno !=''">
                    and j.serialno like concat('%', #{serialno}, '%')
                </if>

            </where>
            order by s.create_time desc,s.application_id desc
        </select>



    <select id="selectOneById" parameterType="com.xm.xzp.model.entity.JgzhSpData" resultType="com.xm.xzp.model.entity.JgzhSpData" >
         select s.id
                ,j.id as jgzh_id
                ,j.serialno
                ,j.accountname
                ,j.accountno
            from  xzp_sp_info s
            left join xzp_jgzh_info j on s.application_id = j.id
            where s.id = #{id}
    </select>

    <!-- 根据id 查询监管账户审核信息详情-->
    <select id="selectInfoById" parameterType="java.lang.String" resultMap="jgzhSpInfoMap">
         select s.id
                ,s.sp_res
                ,coalesce(s.sp_status,'0')  as sp_status
                ,s.sp_content
                ,s.sp_by
                ,s.sp_time
                ,s.sp_classification
                ,s.application_id
                ,j.id as jgzh_id
                ,j.serialno
                ,j.bankid
                ,j.accountname
                ,j.accountno
                ,j.executetype
                ,j.executeamount
                ,j.executedept
                ,j.executedate
                ,j.releaseserialno
                ,j.releasetime
                ,j.note
                ,j.jgzh_status
                ,j.data_source
            from  xzp_sp_info s
            left join xzp_jgzh_info j on s.application_id = j.id
            where s.id = #{id}
    </select>

    <!--新增审核记录-->
    <insert id="insertJgzhSpData" parameterType="com.xm.xzp.model.entity.JgzhData">
        insert into xzp_sp_info (
            id,
            application_id,
            create_time,
            create_by,
            sp_classification
            )
        values (
            to_char(current_timestamp,'yyyyMMddHH24missSSSSS'),
            #{id},
            #{createTime},
            #{createBy},
            'xzp_jgzh_info'
        )
    </insert>
    <!--批量新增审核记录-->
    <insert id="insertJgzhSpDataBatch" parameterType="java.util.List">
        <foreach collection="jgzhDataList" item="item" index="index" separator=";">
            insert into xzp_sp_info (
                application_id,
                create_time,
                create_by,
                sp_classification,
                sp_status
                )
            values (
                #{item.id},
                #{item.createTime},
                #{item.createBy},
                'xzp_jgzh_info',
                '0'
            )
        </foreach>
    </insert>

    <!-- 批量更新审核结果 -->
    <update id="updateBatchSpRes" parameterType="java.util.List">
        <foreach collection="spList" item="item" index="index" separator=";">
            update xzp_sp_info
            set
            <if test="item.spRes !=null and item.spRes != ''">
                sp_res = #{item.spRes},
            </if>
            <if test="item.spContent !=null and item.spContent != ''">
                sp_content = #{item.spContent},
            </if>
            sp_status = '1',
            update_by = #{item.updateBy},
            update_time = #{item.updateTime},
            sp_by = #{item.updateBy},
            sp_time = #{item.updateTime}
            where (sp_res  <![CDATA[<>]]> '1' or sp_res is null )
              and id in
                <foreach collection="item.jgzhSpList" item="flag" open="(" close=")" separator=","  >
                    #{flag}
                </foreach>
        </foreach>
    </update>

    <delete id="deleteByApplyId" parameterType="java.lang.String">
        delete from xzp_sp_info where application_id = #{applyId}
    </delete>


</mapper>

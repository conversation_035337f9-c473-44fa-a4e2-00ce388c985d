package com.xm.xzp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xm.xzp.model.entity.RecSettMismatch;
import com.xm.xzp.model.vo.RecSettMismatchVo;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface RecSettMismatchMapper extends BaseMapper<RecSettMismatch> {
    List<RecSettMismatch> recSettMismatchList(RecSettMismatchVo recSettMismatchVo);

    int updateRecSettMismatch(RecSettMismatch recSettMismatch);
}

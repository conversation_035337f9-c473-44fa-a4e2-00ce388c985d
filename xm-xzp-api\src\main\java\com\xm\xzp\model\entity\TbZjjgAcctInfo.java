package com.xm.xzp.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 资金监管账户信息表
 * <AUTHOR>
 */
@Data
@TableName("tb_zjjg_acct_info")
@ApiModel(value = "TbZjjgAcctInfo", description = "资金监管账户信息")
public class TbZjjgAcctInfo {

    @ApiModelProperty(value = "委托单位代码", required = true)
    @TableField("merch_id")
    @NotBlank(message = "委托单位代码不能为空")
    @Length(max = 32, message = "委托单位代码不能超过32个字符")
    private String merchId;

    @ApiModelProperty(value = "业务代码", required = true)
    @TableField("ope_cd")
    @NotBlank(message = "业务代码不能为空")
    @Length(max = 32, message = "业务代码不能超过32个字符")
    private String opeCd;

    @ApiModelProperty(value = "业务类型 zzg：资金监管")
    @TableField("trans_type")
    @Length(max = 16, message = "业务类型不能超过16个字符")
    private String transType;

    @ApiModelProperty(value = "公司账户", required = true)
    @TableField("cpab_acc_id")
    @NotBlank(message = "公司账户不能为空")
    @Length(max = 32, message = "公司账户不能超过32个字符")
    private String cpabAccId;

    @ApiModelProperty(value = "开户机构号")
    @TableField("open_brh_id")
    @Length(max = 32, message = "开户机构号不能超过32个字符")
    private String openBrhId;

    @ApiModelProperty(value = "开户机构名称")
    @TableField("brh_name")
    @Length(max = 128, message = "开户机构名称不能超过128个字符")
    private String brhName;

    @ApiModelProperty(value = "人行机构号")
    @TableField("pbc_brh_id")
    @Length(max = 32, message = "人行机构号不能超过32个字符")
    private String pbcBrhId;

    @ApiModelProperty(value = "户名", required = true)
    @TableField("acct_nm")
    @NotBlank(message = "户名不能为空")
    @Length(max = 128, message = "户名不能超过128个字符")
    private String acctNm;

    @ApiModelProperty(value = "交易日期")
    @TableField("tran_dt")
    @Length(max = 8, message = "交易日期不能超过8个字符")
    private String tranDt;

    @ApiModelProperty(value = "证件类型")
    @TableField("paper_type")
    @Length(max = 8, message = "证件类型不能超过8个字符")
    private String paperType;

    @ApiModelProperty(value = "证件号码")
    @TableField("paper_id")
    @Length(max = 32, message = "证件号码不能超过32个字符")
    private String paperId;

    @ApiModelProperty(value = "关联账号")
    @TableField("peer_acc_id")
    @Length(max = 32, message = "关联账号不能超过32个字符")
    private String peerAccId;

    @ApiModelProperty(value = "关联户名")
    @TableField("dtl_cstm_nm")
    @Length(max = 128, message = "关联户名不能超过128个字符")
    private String dtlCstmNm;

    @ApiModelProperty(value = "关联账户类型（1-同行，2-跨行）")
    @TableField("acct_type")
    @Length(max = 8, message = "关联账户类型不能超过8个字符")
    private String acctType;

    @ApiModelProperty(value = "交易机构")
    @TableField("txn_brh_id")
    @Length(max = 32, message = "交易机构不能超过32个字符")
    private String txnBrhId;

    @ApiModelProperty(value = "交易时间戳")
    @TableField("tran_time")
    @Length(max = 14, message = "交易时间戳不能超过14个字符")
    private String tranTime;

    @ApiModelProperty(value = "操作员")
    @TableField("tlr_id")
    @Length(max = 16, message = "操作员不能超过16个字符")
    private String tlrId;

    @ApiModelProperty(value = "附加字段1")
    @TableField("oth_msg1_tx")
    @Length(max = 255, message = "附加字段1不能超过255个字符")
    private String othMsg1Tx;

    @ApiModelProperty(value = "附加字段2")
    @TableField("oth_msg2_tx")
    @Length(max = 255, message = "附加字段2不能超过255个字符")
    private String othMsg2Tx;

    @ApiModelProperty(value = "附加字段3")
    @TableField("oth_msg3_tx")
    @Length(max = 255, message = "附加字段3不能超过255个字符")
    private String othMsg3Tx;

    @ApiModelProperty(value = "附加字段4")
    @TableField("oth_msg4_tx")
    @Length(max = 255, message = "附加字段4不能超过255个字符")
    private String othMsg4Tx;

    @ApiModelProperty(value = "附加字段5")
    @TableField("oth_msg5_tx")
    @Length(max = 255, message = "附加字段5不能超过255个字符")
    private String othMsg5Tx;
}
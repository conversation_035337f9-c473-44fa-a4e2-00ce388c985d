package com.xm.xzp.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 资金监管账户信息表
 * <AUTHOR>
 */
@Data
@TableName("tb_zjjg_acct_info")
@ApiModel(value = "TbZjjgAcctInfo", description = "资金监管账户信息")
public class TbZjjgAcctInfo {

    @ApiModelProperty(value = "委托单位代码")
    @TableField("merch_id")
    private String merchId;

    @ApiModelProperty(value = "业务代码")
    @TableField("ope_cd")
    private String opeCd;

    @ApiModelProperty(value = "业务类型 zzg：资金监管")
    @TableField("trans_type")
    private String transType;

    @ApiModelProperty(value = "公司账户")
    @TableField("cpab_acc_id")
    private String cpabAccId;

    @ApiModelProperty(value = "开户机构号")
    @TableField("open_brh_id")
    private String openBrhId;

    @ApiModelProperty(value = "开户机构名称")
    @TableField("brh_name")
    private String brhName;

    @ApiModelProperty(value = "人行机构号")
    @TableField("pbc_brh_id")
    private String pbcBrhId;

    @ApiModelProperty(value = "户名")
    @TableField("acct_nm")
    private String acctNm;

    @ApiModelProperty(value = "交易日期")
    @TableField("tran_dt")
    private String tranDt;

    @ApiModelProperty(value = "证件类型")
    @TableField("paper_type")
    private String paperType;

    @ApiModelProperty(value = "证件号码")
    @TableField("paper_id")
    private String paperId;

    @ApiModelProperty(value = "关联账号")
    @TableField("peer_acc_id")
    private String peerAccId;

    @ApiModelProperty(value = "关联户名")
    @TableField("dtl_cstm_nm")
    private String dtlCstmNm;

    @ApiModelProperty(value = "关联账户类型（1-同行，2-跨行）")
    @TableField("acct_type")
    private String acctType;

    @ApiModelProperty(value = "交易机构")
    @TableField("txn_brh_id")
    private String txnBrhId;

    @ApiModelProperty(value = "交易时间戳")
    @TableField("tran_time")
    private String tranTime;

    @ApiModelProperty(value = "操作员")
    @TableField("tlr_id")
    private String tlrId;

    @ApiModelProperty(value = "附加字段1")
    @TableField("oth_msg1_tx")
    private String othMsg1Tx;

    @ApiModelProperty(value = "附加字段2")
    @TableField("oth_msg2_tx")
    private String othMsg2Tx;

    @ApiModelProperty(value = "附加字段3")
    @TableField("oth_msg3_tx")
    private String othMsg3Tx;

    @ApiModelProperty(value = "附加字段4")
    @TableField("oth_msg4_tx")
    private String othMsg4Tx;

    @ApiModelProperty(value = "附加字段5")
    @TableField("oth_msg5_tx")
    private String othMsg5Tx;
}
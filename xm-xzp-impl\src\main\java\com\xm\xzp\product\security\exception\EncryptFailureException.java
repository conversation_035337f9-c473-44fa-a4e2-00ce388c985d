package com.xm.xzp.product.security.exception;


public class EncryptFailureException extends Exception {
    private String code = "-3";
    private String msg;

    public EncryptFailureException(String msg) {
        this.msg = msg;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}


package com.xm.xzp.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xm.xzp.model.entity.BatchProcessLog;
import com.xm.xzp.model.vo.BatchProcessLogVo;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface BatchProcessLogMapper extends BaseMapper<BatchProcessLog> {

    /**
     * 分页查询批量处理日志
     *
     * @param batchProcessLog 查询条件
     * @return 批量处理日志列表
     */
    List<BatchProcessLog> batchProcessLogList(BatchProcessLogVo batchProcessLog);
}

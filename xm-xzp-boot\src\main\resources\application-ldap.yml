############################## 集成Ldap环境配置 ##############################
# 服务端口号
server:
  port: 9089

# Spring框架相关配置
spring:
  application:
    name: yoaf-server
  # 数据源配置
  datasource:
    url: *****************************************************************
    driver-class-name: org.postgresql.Driver
    username: userapp
    password: userapp
  ldap:
    urls: ldap://*************:389
    base: OU=中国邮政储蓄银行,DC=test,DC=com
    username: CN=cpyfuser,CN=Users,DC=test,DC=com
    password: Psbc!2017
    base-environment:
      java.naming.ldap.attributes.binary: objectGUID
  # 缓存配置
  cache:
    type: redis
  # redis相关配置
  redis:
    database: 3
    host: **************
    port: 6379
    password: 123456
    # 连接超时时间（毫秒）默认是2000ms
    timeout: 2000ms
    # lettuce连接池配置（如不配置则不开启连接池）
    lettuce:
      pool:
        # 连接池大连接数（使用负值表示没有限制）
        max-active: 8
        # 连接池中的大空闲连接
        max-idle: 8
        # 连接池中的小空闲连接
        min-idle: 1
        # 连接池大阻塞等待时间（使用负值表示没有限制）
        max-wait: 1000
        # 关闭超时时间
        shutdown-timeout: 100

# 控制台打印sql
mybatis-plus:
  configuration:
    # SQL日志打印实现类:无日志打印-org.apache.ibatis.logging.nologging.NoLoggingImpl
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# JAVA开发平台相关配置
pfpj:
  # SDK中esb组件配置
  esb:
    # esb是否启用
    enabled: false
    # esb接口地址 测试环境
    business-url-map:
      sms: http://************:8013/direct/TMSSendShortMessage
  # yoaf自定义配置
  yoaf:
    # yoaf缓存模块配置
    cache:
      # yoaf自定义Redis配置
      redis:
        # 缓存配置列表(开发坏境建议)
        other-cache-configs:
          # 手机验证码缓存(默认3分钟)
          - cache-name: yoaf:auth-center:sms-captcha
            ttl: 180
          # 图片验证码缓存(默认5分钟)
          - cache-name: yoaf:auth-center:image-captcha
            ttl: 300
          # 在线用户信息(默认1小时)
          - cache-name: yoaf:auth-center:online-user
            ttl: 3600
          # 黑白名单缓存(默认1小时)
          - cache-name: yoaf:auth-center:black-white
            ttl: 3600
          # 业务字典缓存(默认1小时)
          - cache-name: yoaf:admin:dict-entry
            ttl: 3600
          # 审计日志配置缓存(默认1小时)
          - cache-name: yoaf:auditlog:auditlog-config
            ttl: 3600
          # 功能权限信息缓存(默认1小时)
          - cache-name: yoaf:auth-center:function
            ttl: 3600
          # 角色资源关联信息缓存(默认1小时)
          - cache-name: yoaf:auth-center:res-role
            ttl: 3600
          # 本地授权信息缓存(默认1小时)
          - cache-name: yoaf:auth-center:local-auth
            ttl: 3600
    # yoaf审计日志配置
    auditlog:
      # 审计日志配置策略(no-cache:无缓存;in-memory:默认内存;spring-cache:Spring缓存)
      auditlog-config-policy: spring-cache
    # yoaf权限认证中心相关配置
    auth:
      core:
        # 是否开启YOAF的黑白名单过滤器
        black-white-enabled: false
        # 是否开启YOAF的本地授权过滤器
        local-auth-enabled: true
        # 忽略资源地址集合(以,分隔)
        ignoring-ant-patterns: /,/csrf,/error,/*.ico,/webjars/**,/swagger-resources/**,/v2/api-docs,/**.html,/api/auth/center/login,/api/auth/center/captcha/**,/api/auth/center/login/public/key,/api/auth/center/actions/refresh,/api/auth/center/auth/mode,/actuator/health,/actuator/info
#        ignoring-ant-patterns: /**
      # 登录参数配置
      login:
        # 登录核心配置
        core:
          # 令牌策略(stateless:默认无状态且多端登录, store:存储但限一端登录, storeMultiple:存储且多端登录)
          token-policy: storeMultiple
          # 图片验证码
          image-captcha: true
          # 自定义登录地址
          login-url: /api/auth/center/login
          # 自定义登出地址
          logout-url: /api/auth/center/logout
    # swagger配置
    swagger:
      # 是否开启swagger-默认开启(关闭之后则不能访问swagger页面)
      enabled: true
      # 是否支持提交请求-默认关闭（关闭之后用户只能查看接口api不能发送请求）
      submit-enabled: true
      # 是否将actuator开放端点地址加入到swagger中-默认关闭（加入swagger中方便安全访问）
      actuator-enabled: true
# 设置actuator开放端口(默认全部)
management:
  endpoints:
    web:
      exposure:
        include: '*'

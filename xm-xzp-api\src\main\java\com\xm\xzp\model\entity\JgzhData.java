package com.xm.xzp.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;


/**
 * @Description: 监管账户变动情况反馈信息表
 * @Author: Lyl
 * @Date: 2024-10-14
 * @Version: V1.0
 */
@Data
@TableName("xzp_jgzh_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "xzp_jgzh_info对象", description = "监管账户变动情况反馈信息表")
public class JgzhData implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private String updateTime;
    /**
     * 执行流水号
     */
    @ApiModelProperty(value = "执行流水号")
    private String serialno;

    /**
     * 开户银行代码
     */
    @ApiModelProperty(value = "开户银行代码")
    private String bankid;

    /**
     * 账户名
     */
    @ApiModelProperty(value = "账户名")
    private String accountname;

    /**
     * 账户号
     */
    @ApiModelProperty(value = "账户号")
    private String accountno;

    /**
     * 执行类型
     */
    @ApiModelProperty(value = "执行类型")
    private String executetype;

    /**
     * 执行金额
     */
    @ApiModelProperty(value = "执行金额")
    private String executeamount;

    /**
     * 执行部门
     */
    @ApiModelProperty(value = "执行部门")
    private String executedept;

    /**
     * 执行时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "执行时间")
    private String executedate;

    /**
     * 解除执行原流水号
     */
    @ApiModelProperty(value = "解除执行原流水号")
    private String releaseserialno;

    /**
     * 解除执行时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "解除执行时间")
    private String releasetime;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    private String note;

    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源")
    private String dataSource;


    /**
     * 监管账户状态
     */
    @ApiModelProperty(value = "监管账户状态")
    private String jgzhStatus;

    /**导入批次信息*/
    private String impBatch;

    /**
     * 监管账户反馈结果说明
     */
    @ApiModelProperty(value = "监管账户反馈结果说明")
    private String jgzhDesc;

    /**
     * 敏感信息状态 1-脱敏 2-未脱敏
     */
    private transient String sensitiveStatus;

    /**
     * 用户id列表
     */
    @Size(min = 1, message = "请至少选择一条记录")
    private transient List<JgzhData> jgzhList;
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xm-xzp</artifactId>
        <groupId>com.xm.xzp</groupId>
        <version>1.1.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>xm-xzp-impl</artifactId>
    <name>xm-xzp-impl</name>
    <description>新中平管理-功能实现模块</description>

    <dependencies>
        <!-- springboot-web模块[已提供依赖范围] -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- 公共-定制化模块 -->
        <dependency>
            <groupId>com.psbc.pfpj</groupId>
            <artifactId>yoaf-common-custom</artifactId>
        </dependency>
        <!-- 公共-mybatis-plus配置模块 -->
        <dependency>
            <groupId>com.psbc.pfpj</groupId>
            <artifactId>yoaf-common-mybatis-plus</artifactId>
        </dependency>
        <!-- 公共-缓存模块 -->
        <dependency>
            <groupId>com.psbc.pfpj</groupId>
            <artifactId>yoaf-common-cache</artifactId>
        </dependency>
        <!-- 公共-返回对象工具类 -->
        <dependency>
            <groupId>com.psbc.pfpj</groupId>
            <artifactId>yoaf-common-response-result</artifactId>
        </dependency>
        <!-- 公共-hibernate验证模块 -->
        <dependency>
            <groupId>com.psbc.pfpj</groupId>
            <artifactId>yoaf-common-hibernate-validator</artifactId>
        </dependency>
        <!-- 用户信息获取模块 -->
        <dependency>
            <groupId>com.psbc.pfpj</groupId>
            <artifactId>yoaf-common-user-context</artifactId>
        </dependency>
        <!-- 厦门新中平-功能接口模块 -->
        <dependency>
            <groupId>com.xm.xzp</groupId>
            <artifactId>xm-xzp-api</artifactId>
        </dependency>
        <!-- hutool-all工具类-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <!-- pagehelper分页 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <!-- 公共-swagger配置模块 -->
        <dependency>
            <groupId>com.psbc.pfpj</groupId>
            <artifactId>yoaf-common-swagger</artifactId>
        </dependency>
        <!-- easypoi 导入导出工具包 -->
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>5.2.15.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>

        <!--XML解析-->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.9</version>
        </dependency>
        <!-- Quartz定时任务 -->
       <!-- <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.4.0</version>
            <scope>compile</scope>
        </dependency>-->



    </dependencies>

</project>
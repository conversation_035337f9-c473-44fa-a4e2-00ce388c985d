package com.xm.xzp.api;


import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.entity.JgzhData;
import com.xm.xzp.model.entity.JgzhSpData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * <AUTHOR>
 */
@ResponseBody
@RequestMapping("/api/admin/xzp")
@Api(tags = "新中平管理")
@Validated
public interface JgzhShApi {
  /**
     * 监管账户信息变动情况审核列表查询
     *
     * @param pageNum    当前页
     * @param jgzhSpData 接收用户数据
     * @param pageSize   每页沙宣数量
     * @return 数据列表
     */
    @ApiOperation(value = "监管账户信息变动情况审核列表", notes = "jgzhSpList")
    @PostMapping("/jgzhSpList")
    RestResponse<PageInfo<JgzhSpData>> jgzhSpList(@RequestBody JgzhSpData jgzhSpData, @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum, @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize);

    @ApiOperation(value = "监管账户被执行信息审核详情", notes = "jgzhSpDetail")
    @GetMapping("/jgzhSpDetail/{id}")
    RestResponse<JgzhSpData> jgzhSpDetail(@PathVariable("id") String id);

    @ApiOperation(value = "审核监管账户被执行信息状态", notes = "spJgzh")
    @PostMapping("/spJgzh")
    RestResponse<JgzhSpData> spJgzh(@RequestBody JgzhSpData jgzhSpData);

    @ApiOperation(value = "批量审核监管账户被执行信息状态", notes = "spJgzhBatch")
    @PostMapping("/spJgzhBatch")
    RestResponse<JgzhSpData> batchSpJgzh(@RequestBody JgzhSpData jgzhSpData);

    @ApiOperation(value = "查询敏感信息", notes = "getJgzhSpSensitiveInfo")
    @GetMapping("/jgzhSp/sensitive/get/{id}/{sensitiveStatus}")
    RestResponse<JgzhSpData> getJgzhSensitiveInfo(@PathVariable("id") String id,
                                                @PathVariable("sensitiveStatus") String sensitiveStatus);



}

package com.xm.xzp.api;

import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.entity.OpeCd;
import com.xm.xzp.model.vo.MerchOpeGroupVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@ResponseBody
@RequestMapping("/api/admin/xzp")
@Api(tags = "业务代码与委托代码数据源列表")
@Validated
public interface MerchOpeApi {
    @ApiOperation(value = "按商户号分组查询列表", notes = "listGroupByMerchId")
    @PostMapping("/listGroupByMerchId")
    RestResponse<List<MerchOpeGroupVo>> listGroupByMerchId(@RequestBody MerchOpeGroupVo merchOpeGroupVo);

    @ApiOperation(value = "按操作码分组查询列表", notes = "listGroupByOpeCd")
    @PostMapping("/listGroupByOpeCd")
    RestResponse<List<MerchOpeGroupVo>> listGroupByOpeCd(@RequestBody OpeCd merchOpeGroupVo);
}

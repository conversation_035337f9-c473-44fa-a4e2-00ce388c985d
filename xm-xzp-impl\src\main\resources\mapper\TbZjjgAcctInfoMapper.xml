<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xm.xzp.mapper.TbZjjgAcctInfoMapper">
    
    <resultMap id="BaseResultMap" type="com.xm.xzp.model.entity.TbZjjgAcctInfo">
        <result column="merch_id" jdbcType="VARCHAR" property="merchId"/>
        <result column="ope_cd" jdbcType="VARCHAR" property="opeCd"/>
        <result column="trans_type" jdbcType="VARCHAR" property="transType"/>
        <result column="cpab_acc_id" jdbcType="VARCHAR" property="cpabAccId"/>
        <result column="open_brh_id" jdbcType="VARCHAR" property="openBrhId"/>
        <result column="brh_name" jdbcType="VARCHAR" property="brhName"/>
        <result column="pbc_brh_id" jdbcType="VARCHAR" property="pbcBrhId"/>
        <result column="acct_nm" jdbcType="VARCHAR" property="acctNm"/>
        <result column="tran_dt" jdbcType="VARCHAR" property="tranDt"/>
        <result column="paper_type" jdbcType="VARCHAR" property="paperType"/>
        <result column="paper_id" jdbcType="VARCHAR" property="paperId"/>
        <result column="peer_acc_id" jdbcType="VARCHAR" property="peerAccId"/>
        <result column="dtl_cstm_nm" jdbcType="VARCHAR" property="dtlCstmNm"/>
        <result column="acct_type" jdbcType="VARCHAR" property="acctType"/>
        <result column="txn_brh_id" jdbcType="VARCHAR" property="txnBrhId"/>
        <result column="tran_time" jdbcType="VARCHAR" property="tranTime"/>
        <result column="tlr_id" jdbcType="VARCHAR" property="tlrId"/>
        <result column="oth_msg1_tx" jdbcType="VARCHAR" property="othMsg1Tx"/>
        <result column="oth_msg2_tx" jdbcType="VARCHAR" property="othMsg2Tx"/>
        <result column="oth_msg3_tx" jdbcType="VARCHAR" property="othMsg3Tx"/>
        <result column="oth_msg4_tx" jdbcType="VARCHAR" property="othMsg4Tx"/>
        <result column="oth_msg5_tx" jdbcType="VARCHAR" property="othMsg5Tx"/>
    </resultMap>

    <!-- 根据关键字模糊查询账户信息（用于联想下拉控件），keyword为空时返回全量数据 -->
    <select id="selectAcctInfoByKeyword" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
            merch_id,
            ope_cd,
            trans_type,
            cpab_acc_id,
            open_brh_id,
            brh_name,
            pbc_brh_id,
            acct_nm,
            tran_dt,
            paper_type,
            paper_id,
            peer_acc_id,
            dtl_cstm_nm,
            acct_type,
            txn_brh_id,
            tran_time,
            tlr_id,
            oth_msg1_tx,
            oth_msg2_tx,
            oth_msg3_tx,
            oth_msg4_tx,
            oth_msg5_tx
        FROM public.tb_zjjg_acct_info
        <where>
            <if test="keyword != null and keyword != ''">
                (
                    acct_nm LIKE CONCAT('%', #{keyword}, '%')
                    OR cpab_acc_id LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
        ORDER BY acct_nm, cpab_acc_id
        <if test="keyword != null and keyword != ''">
            LIMIT 100
        </if>
    </select>

    <!-- 根据条件分页查询账户信息 -->
    <select id="selectAcctInfoByCondition" parameterType="com.xm.xzp.model.vo.AcctInfoQueryVo" resultMap="BaseResultMap">
        SELECT
            merch_id,
            ope_cd,
            trans_type,
            cpab_acc_id,
            open_brh_id,
            brh_name,
            pbc_brh_id,
            acct_nm,
            tran_dt,
            paper_type,
            paper_id,
            peer_acc_id,
            dtl_cstm_nm,
            acct_type,
            txn_brh_id,
            tran_time,
            tlr_id,
            oth_msg1_tx,
            oth_msg2_tx,
            oth_msg3_tx,
            oth_msg4_tx,
            oth_msg5_tx
        FROM public.tb_zjjg_acct_info
        <where>
            <if test="queryVo.opeCd != null and queryVo.opeCd != ''">
                AND ope_cd = #{queryVo.opeCd}
            </if>
            <if test="queryVo.merchId != null and queryVo.merchId != ''">
                AND merch_id = #{queryVo.merchId}
            </if>
            <if test="queryVo.openBrhId != null and queryVo.openBrhId != ''">
                AND open_brh_id LIKE CONCAT('%', #{queryVo.openBrhId}, '%')
            </if>
            <if test="queryVo.pbcBrhId != null and queryVo.pbcBrhId != ''">
                AND pbc_brh_id LIKE CONCAT('%', #{queryVo.pbcBrhId}, '%')
            </if>
            <if test="queryVo.cpabAccId != null and queryVo.cpabAccId != ''">
                AND cpab_acc_id LIKE CONCAT('%', #{queryVo.cpabAccId}, '%')
            </if>
            <if test="queryVo.acctNm != null and queryVo.acctNm != ''">
                AND acct_nm LIKE CONCAT('%', #{queryVo.acctNm}, '%')
            </if>
            <if test="queryVo.peerAccId != null and queryVo.peerAccId != ''">
                AND peer_acc_id LIKE CONCAT('%', #{queryVo.peerAccId}, '%')
            </if>
            <if test="queryVo.dtlCstmNm != null and queryVo.dtlCstmNm != ''">
                AND dtl_cstm_nm LIKE CONCAT('%', #{queryVo.dtlCstmNm}, '%')
            </if>
        </where>
        ORDER BY tran_time DESC, acct_nm, cpab_acc_id
    </select>

</mapper>
package com.xm.xzp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <p>
 * 安全配置参数
 * </p>
 *
 * @version 1.0.0
 * <AUTHOR>
 * @date 2022-03-03 17:28
 */
@Data
@ConfigurationProperties(prefix = "xm.xzp.security")
public class AdminSecurityConfig {

    /**
     * 密码过期时间(单位：天)
     */
    private Integer pwdExpireTime = 30;

    /**
     * 用户密码默认生成规则
     */
    private String userPwdRule = "fix:Ps123456#";
}

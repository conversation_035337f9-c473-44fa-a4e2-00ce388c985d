package com.xm.xzp.controller;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.psbc.pfpj.yoaf.response.util.Constants;
import com.xm.xzp.api.BatchTranCtrlApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.entity.BatchTranCtrl;
import com.xm.xzp.model.vo.BatchTranCtrlVo;
import com.xm.xzp.service.IBatchTranCtrlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@Component
public class BatchTranCtrlController implements BatchTranCtrlApi {

    @Resource
    private IBatchTranCtrlService batchTranCtrlService;

    @Override
    @PMCTLLog(name = "查询批量交易列表", action = "query")
    public RestResponse<PageInfo<BatchTranCtrl>> batchTranCtrlList(BatchTranCtrlVo batchTranCtrl,
                                                                   Integer pageNum, Integer pageSize) {
        PageInfo<BatchTranCtrl> pageList = batchTranCtrlService.batchTranCtrlList(
                batchTranCtrl, pageNum, pageSize);
        return RestResponse.success(pageList);
    }

    @Override
    @PMCTLLog(name = "编辑批量交易", action = "修改")
    public RestResponse<String> editBatchTranCtrl(@RequestBody BatchTranCtrl batchTranCtrl) {
        log.debug("修改批量交易执行参数：{}", batchTranCtrl.toString());
        boolean flag = batchTranCtrlService.editBatchTranCtrl(batchTranCtrl);
        if (flag) {
            return RestResponse.success(Constants.DEFAULT_SUCCESS_EDIT_MESSAGE);
        } else {
            return RestResponse.fail(Constants.DEFAULT_FAILED_EDIT_MESSAGE);
        }
    }
}
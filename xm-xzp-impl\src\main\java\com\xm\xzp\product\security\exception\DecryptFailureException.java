package com.xm.xzp.product.security.exception;


/**
 * <AUTHOR>
 */
public class DecryptFailureException extends Exception {
    private String code = "-2";
    private String msg;

    public DecryptFailureException(String msg) {
        this.msg = msg;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}

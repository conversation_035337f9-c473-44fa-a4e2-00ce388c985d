<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xm.xzp.mapper.DictEntryMapper">
    <resultMap id="BaseResultUserMap" type="com.xm.xzp.model.entity.DictEntry">
        <result column="dict_type_id" jdbcType="VARCHAR" property="dictTypeId"/>
        <result column="dict_id" jdbcType="VARCHAR" property="dictId"/>
        <result column="dict_name" jdbcType="VARCHAR" property="dictName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="display_order" jdbcType="INTEGER" property="displayOrder"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="TIMESTAMP" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!--  查询字典实体列表  -->
    <select id="selectDictEntry" resultMap="BaseResultUserMap">
        select
            dict_type_id, dict_id, dict_name, status, display_order,
            create_user,create_time,update_user,update_time
        from bc_pmctl_dict_entry
        <where>
            <if test="query.dictTypeId != null and query.dictTypeId !=''">
                and dict_type_id = #{query.dictTypeId,jdbcType=VARCHAR}
            </if>
            <if test="query.dictId != null and query.dictId !=''">
                and dict_id like concat('%', #{query.dictId,jdbcType=VARCHAR}, '%')
            </if>
            <if test="query.dictName != null and query.dictName !=''">
                and dict_name like concat('%', #{query.dictName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="query.status != null and query.status !=''">
                and status = #{query.status,jdbcType=VARCHAR}
            </if>
        </where>
        order by display_order asc, create_time desc
    </select>

    <!-- 批量添加或更新字典信息 -->
   <!-- <insert id="insertOrUpdateBatch" parameterType="com.psbc.pfpj.yoaf.admin.model.entity.DictEntry">
        insert into bc_pmctl_dict_entry (dict_type_id, dict_id, dict_name, status, display_order,
            create_user,create_time,update_user,update_time)
            values
        <foreach collection="dictEntrySet" item="dictEntry" index="index" separator="," >
            (#{dictEntry.dictTypeId,jdbcType=VARCHAR}, #{dictEntry.dictId,jdbcType=VARCHAR}, #{dictEntry.dictName,jdbcType=VARCHAR},
             #{dictEntry.status,jdbcType=VARCHAR},#{dictEntry.displayOrder,jdbcType=INTEGER},
            #{dictEntry.createUser,jdbcType=VARCHAR},#{dictEntry.createTime,jdbcType=TIMESTAMP},
            #{dictEntry.updateUser,jdbcType=VARCHAR},#{dictEntry.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
        ON CONFLICT(dict_type_id, dict_id) DO UPDATE SET
        (dict_name,status, display_order)= (excluded.dict_name,excluded.status,excluded.display_order)
    </insert>-->
</mapper>

package com.xm.xzp.model.vo;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class IntTxnLogResultVo {
    private String tranDt;
    private String localTm;
    private String tranInstId;
    private String tlrId;
    private String tranCd;
    private String othMsg7Tx;
    private String opeCd;
    private String opeNm;
    private String merchId;
    private String merchNm;
    private String subOpeCd;
    private String subOpeNm;
    private String cstmId;
    private String accCardId;
    private BigDecimal tranAt;
    private String invoiceNum;
    private String payCd;
    private String tranStatCd;
    private String rspCd;
    private String othPrMsg3;
    private String logId;
    private String payId;
}
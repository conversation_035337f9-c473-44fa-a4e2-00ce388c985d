package com.xm.xzp.controller;

import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.api.AcctInfoApi;
import com.xm.xzp.model.entity.TbZjjgAcctInfo;
import com.xm.xzp.service.ITbZjjgAcctInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 账户信息查询控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@Component
public class AcctInfoController implements AcctInfoApi {

    @Resource
    private ITbZjjgAcctInfoService tbZjjgAcctInfoService;

    @Override
    public RestResponse<List<TbZjjgAcctInfo>> suggestAcctInfo(String keyword) {
        log.debug("账户信息联想查询，参数：{}", keyword);
        List<TbZjjgAcctInfo> result = tbZjjgAcctInfoService.suggestAcctInfo(keyword);
        log.debug("账户信息联想查询结果数量：{}", result.size());
        return RestResponse.success(result);
    }
}
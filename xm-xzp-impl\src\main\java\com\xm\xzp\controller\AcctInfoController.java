package com.xm.xzp.controller;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.api.AcctInfoApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.entity.TbZjjgAcctInfo;
import com.xm.xzp.model.vo.AcctInfoQueryVo;
import com.xm.xzp.service.ITbZjjgAcctInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 账户信息查询控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@Component
public class AcctInfoController implements AcctInfoApi {

    @Resource
    private ITbZjjgAcctInfoService tbZjjgAcctInfoService;

    @Override
    public RestResponse<List<TbZjjgAcctInfo>> suggestAcctInfo(String keyword) {
        log.debug("账户信息联想查询，参数：{}", keyword);
        List<TbZjjgAcctInfo> result = tbZjjgAcctInfoService.suggestAcctInfo(keyword);
        log.debug("账户信息联想查询结果数量：{}", result.size());
        return RestResponse.success(result);
    }

    @Override
    @PMCTLLog(name = "账户信息分页查询", action = "查询")
    public RestResponse<PageInfo<TbZjjgAcctInfo>> queryAcctInfoList(AcctInfoQueryVo queryVo, Integer pageNum, Integer pageSize) {
        log.debug("账户信息分页查询，查询条件：{}，页码：{}，每页数量：{}", queryVo, pageNum, pageSize);
        PageInfo<TbZjjgAcctInfo> pageInfo = tbZjjgAcctInfoService.queryAcctInfoPage(queryVo, pageNum, pageSize);
        log.debug("账户信息分页查询结果，总数：{}，当前页数据量：{}", pageInfo.getTotal(), pageInfo.getList().size());
        return RestResponse.success(pageInfo);
    }

    @Override
    @PMCTLLog(name = "新增账户信息", action = "新增")
    public RestResponse<String> addAcctInfo(TbZjjgAcctInfo acctInfo) {
        log.debug("新增账户信息，参数：{}", acctInfo);

        // 检查必填字段
        if (acctInfo.getCpabAccId() == null || acctInfo.getCpabAccId().trim().isEmpty()) {
            return RestResponse.fail("公司账户不能为空");
        }
        if (acctInfo.getAcctNm() == null || acctInfo.getAcctNm().trim().isEmpty()) {
            return RestResponse.fail("户名不能为空");
        }
        if (acctInfo.getOpeCd() == null || acctInfo.getOpeCd().trim().isEmpty()) {
            return RestResponse.fail("业务代码不能为空");
        }
        if (acctInfo.getMerchId() == null || acctInfo.getMerchId().trim().isEmpty()) {
            return RestResponse.fail("委托单位代码不能为空");
        }

        boolean result = tbZjjgAcctInfoService.addAcctInfo(acctInfo);
        if (result) {
            log.info("新增账户信息成功，账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            return RestResponse.success("新增账户信息成功");
        } else {
            log.error("新增账户信息失败，账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            return RestResponse.fail("新增账户信息失败，可能是账户信息已存在");
        }
    }
}
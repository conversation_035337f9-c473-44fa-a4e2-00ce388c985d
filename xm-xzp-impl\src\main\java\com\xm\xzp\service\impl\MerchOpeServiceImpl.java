package com.xm.xzp.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xm.xzp.mapper.MerchOpeMapper;
import com.xm.xzp.model.entity.OpeCd;
import com.xm.xzp.model.vo.MerchOpeGroupVo;
import com.xm.xzp.service.IMerchOpeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class MerchOpeServiceImpl implements IMerchOpeService {

    @Resource
    private MerchOpeMapper merchOpeMapper;

    @Override
    @DS("datasource2")
    public List<MerchOpeGroupVo> listGroupByMerchId(MerchOpeGroupVo query) {
        log.debug("按商户号分组查询，参数：{}", query);
        return merchOpeMapper.selectGroupByMerchId(query);
    }

    @Override
    @DS("datasource2")
    public List<MerchOpeGroupVo> listGroupByOpeCd(OpeCd query) {
        log.debug("按操作码分组查询，参数：{}", query);
        return merchOpeMapper.selectGroupByOpeCd(query);
    }
}
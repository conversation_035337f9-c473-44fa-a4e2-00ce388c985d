package com.xm.xzp.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

@Data
@TableName("tb_merch_sub_ope")
public class MerchSubOpe {
    @TableId
    private String merchId;
    @TableId
    private String opeCd;
    @TableId
    private String subOpeId;
    private String subOpeNm;
    private String spMerchId;
    private String merchInstId;
    private String busiMerchId;
    private String billId;
    private String subMerchOnOffFg;
    private String openDt;
    private String whiteListFg;
    private String summCd;
    private String cpcbSummCd;
    private String payLimitNum;
    private String minPayAt;
    private String maxPayAt;
    private String dueTerm;
    private String delayRate;
    private String flag01;
    private String flag02;
    private String memo01;
    private String memo02;
    private String lastModifyTlrId;
    private String lastModifyInstId;
    private Date lastModifyDt;
}
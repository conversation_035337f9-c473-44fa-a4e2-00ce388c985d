package com.xm.xzp.model.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;


/**
 * @Description: 监管账户变动情况反馈信息审核表
 * @Author: Lyl
 * @Date: 2024-12-9
 * @Version: V1.0
 */
@Data
@TableName("xzp_sp_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "xzp_sp_info对象", description = "监管账户变动情况反馈信息审核表")
public class JgzhSpData implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**创建日期*/
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
//    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private String createTime;
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**更新日期*/
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
//    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private String updateTime;

    /**申请id*/
//    @Excel(name = "申请id", width = 15)
    @ApiModelProperty(value = "申请id")
    private String applicationId;

    /**审批人*/
    @ApiModelProperty(value = "审批人")
    private String spBy;
    /**审批日期*/
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
//    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审批日期")
    private String spTime;

    /**审批分类*/
    @Excel(name = "审批分类", width = 15)
    @ApiModelProperty(value = "审批分类")
    private String spClassification;
    /**结果*/
    @Excel(name = "结果", width = 15)
    @ApiModelProperty(value = "结果")
    private String spRes;
    /**审批意见内容*/
    @Excel(name = "审批意见内容", width = 15)
    @ApiModelProperty(value = "审批意见内容")
    private String spContent;
    /**审核状态，0-待审核，1已审核*/
    @Excel(name = "审核状态", width = 15)
    @ApiModelProperty(value = "审核状态")
    private String spStatus;

    private transient String jgzhId;
    private transient String serialno;
    private transient String bankid;
    private transient String accountname;
    private transient String accountno;
    private transient String executetype;
    private transient String executeamount;
    private transient String executedept;
    private transient String executedate;
    private transient String releaseserialno;
    private transient String releasetime;
    private transient String note;
    private transient String dataSource;
    private transient String jgzhStatus;
    /**
     * 用户名列表
     */
    @Size(min = 1, message = "请至少选择一条记录")
    private transient List<JgzhSpData> jgzhSpList;

    /**
     * 敏感信息状态 1-脱敏 2-未脱敏
     */
    private transient String sensitiveStatus;

}

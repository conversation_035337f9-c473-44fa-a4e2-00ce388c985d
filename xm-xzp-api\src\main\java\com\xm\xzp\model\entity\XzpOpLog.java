package com.xm.xzp.model.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: TODO
 * @create 2024-12-10 10:43
 */
@Data
@TableName("xzp_op_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "xzp_op_log对象", description = "新中平操作日志表日志表")
public class XzpOpLog implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @ApiModelProperty(value = "id主键")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private String createTime;
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private String updateTime;
    /**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**申请单类型（1：数据申请，2：数据上传申请，3：数据下载申请）*/
    @Excel(name = "申请单类型（1：数据申请，2：数据上传申请，3：数据下载申请）", width = 15)
    @ApiModelProperty(value = "申请单类型（1：数据申请，2：数据上传申请，3：数据下载申请）")
    private String applyType;
    /**申请单id*/
    @Excel(name = "申请单id", width = 15)
    @ApiModelProperty(value = "申请单id")
    private String applyId;
    /**流程id*/
    @Excel(name = "流程id", width = 15)
    @ApiModelProperty(value = "流程id")
    private String flowId;
    /**节点id*/
    @Excel(name = "节点id", width = 15)
    @ApiModelProperty(value = "节点id")
    private String stepId;
    /**环节名称*/
    @Excel(name = "环节名称", width = 15)
    @ApiModelProperty(value = "环节名称")
    private String stepName;
    /**处理人*/
    @Excel(name = "处理人", width = 15)
    @ApiModelProperty(value = "处理人")
    private String dealUser;
    /**处理人所属机构*/
    @Excel(name = "处理人所属机构（部门）",width = 15)
    @ApiModelProperty(value = "处理人所属机构")
    private String dealOrg;
    /**处理结果*/
    @Excel(name = "处理结果", width = 15)
    @ApiModelProperty(value = "处理结果")
    private String dealRes;
    /**处理意见*/
    @Excel(name = "处理意见", width = 15)
    @ApiModelProperty(value = "处理意见")
    private String opinion;
    /**开始时间*/
    @Excel(name = "开始时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    /**结束时间*/
    @Excel(name = "结束时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    /**特殊关联id*/
    @Excel(name = "特殊关联id", width = 15)
    @ApiModelProperty(value = "特殊关联id")
    private String speReleId;
    /**预留字段1*/
    @Excel(name = "预留字段1", width = 15)
    @ApiModelProperty(value = "预留字段1")
    private String reserveField1;
    /**预留字段2*/
    @Excel(name = "预留字段2", width = 15)
    @ApiModelProperty(value = "预留字段2")
    private String reserveField2;
    /**预留字段3*/
    @Excel(name = "预留字段3", width = 15)
    @ApiModelProperty(value = "预留字段3")
    private String reserveField3;
    /**预留字段4*/
    @Excel(name = "预留字段4", width = 15)
    @ApiModelProperty(value = "预留字段4")
    private String reserveField4;
    /**预留字段5*/
    @Excel(name = "预留字段5", width = 15)
    @ApiModelProperty(value = "预留字段5")
    private String reserveField5;

    /**
     * 创建日期_开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private transient String createTimeStart;
    /**
     * 创建日期_结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private transient String createTimeEnd;

    /**
     * 敏感信息状态 1-脱敏 2-未脱敏
     */
    private transient String sensitiveStatus;

}

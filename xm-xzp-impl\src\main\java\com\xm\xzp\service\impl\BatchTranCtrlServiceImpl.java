package com.xm.xzp.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.exception.BizException;
import com.xm.xzp.enums.SensitiveStatusEnum;
import com.xm.xzp.mapper.BatchTranCtrlMapper;
import com.xm.xzp.model.entity.BatchTranCtrl;
import com.xm.xzp.model.entity.JgzhData;
import com.xm.xzp.model.vo.BatchTranCtrlVo;
import com.xm.xzp.service.IBatchTranCtrlService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class BatchTranCtrlServiceImpl extends ServiceImpl<BatchTranCtrlMapper, BatchTranCtrl> implements IBatchTranCtrlService {

    @Resource
    private BatchTranCtrlMapper batchTranCtrlMapper;

    /**
     * 查询批量交易控制列表
     *
     * @param batchTranCtrl 查询条件
     * @return 分页信息
     */
    @Override
    @DS("datasource2")
    public PageInfo<BatchTranCtrl> batchTranCtrlList(BatchTranCtrlVo batchTranCtrl, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<BatchTranCtrl> list = this.list(batchTranCtrlQueryWrapper(batchTranCtrl));
        return new PageInfo<>(list);
    }
    /**
     * 构建查询条件
     *
     * @param batchTranCtrl 查询条件对象
     * @return QueryWrapper
     */
    private QueryWrapper<BatchTranCtrl> batchTranCtrlQueryWrapper(BatchTranCtrlVo batchTranCtrl) {
        QueryWrapper<BatchTranCtrl> queryWrapper = new QueryWrapper<>();
        if (batchTranCtrl != null) {
            LambdaQueryWrapper<BatchTranCtrl> lambdaQueryWrapper = queryWrapper.lambda();
            if ( StringUtils.isNotBlank(batchTranCtrl.getStartTime()) && StringUtils.isNotBlank(batchTranCtrl.getEndTime())) {
                lambdaQueryWrapper.le(BatchTranCtrl::getTranDt, batchTranCtrl.getEndTime());
                lambdaQueryWrapper.ge(BatchTranCtrl::getTranDt, batchTranCtrl.getStartTime());
            }
            if (StringUtils.isNotBlank(batchTranCtrl.getMerchId())) {
                lambdaQueryWrapper.eq(BatchTranCtrl::getMerchId, batchTranCtrl.getMerchId());
            }
            if (StringUtils.isNotBlank(batchTranCtrl.getOpeCd())) {
                lambdaQueryWrapper.eq(BatchTranCtrl::getOpeCd, batchTranCtrl.getOpeCd());
            }
            if (StringUtils.isNotBlank(batchTranCtrl.getAnsCd())) {
                lambdaQueryWrapper.eq(BatchTranCtrl::getAnsCd, batchTranCtrl.getAnsCd());
            }
            //处理标志
            if (StringUtils.isNotBlank(batchTranCtrl.getProcFg())) {
                lambdaQueryWrapper.eq(BatchTranCtrl::getProcFg, batchTranCtrl.getProcFg());
            }
            //交易代码
            if (StringUtils.isNotBlank(batchTranCtrl.getTranCd())) {
                lambdaQueryWrapper.eq(BatchTranCtrl::getTranCd, batchTranCtrl.getTranCd());
            }
        }
        queryWrapper.orderByDesc("tran_dt");
        return queryWrapper;
    }

    @Override
    @DS("datasource2")
    public BatchTranCtrl getOne(Wrapper<BatchTranCtrl> queryWrapper) {
        return super.getOne(queryWrapper);
    }

    /**
     * 编辑批量交易
     *
     * @param batchTranCtrl 批量交易信息
     * @return 是否成功
     */
    @Override
    @DS("datasource2")
    public boolean editBatchTranCtrl(BatchTranCtrl batchTranCtrl) {
        // 参数校验
        if (StringUtils.isBlank(batchTranCtrl.getBatchId())
                || StringUtils.isBlank(batchTranCtrl.getMerchId())
                || StringUtils.isBlank(batchTranCtrl.getOpeCd())) {
            throw new BizException("必填参数不能为空");
        }

        // 检查记录是否存在
        LambdaQueryWrapper<BatchTranCtrl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BatchTranCtrl::getBatchId, batchTranCtrl.getBatchId())
                .eq(BatchTranCtrl::getMerchId, batchTranCtrl.getMerchId())
                .eq(BatchTranCtrl::getOpeCd, batchTranCtrl.getOpeCd());

        BatchTranCtrl existRecord = this.getOne(queryWrapper);
        if (existRecord == null) {
            throw new BizException("记录不存在");
        }

        // 设置更新时间等审计字段
        batchTranCtrl.setLastModifyTm(DateUtil.format(new Date(), "yyyyMMddHHmmss"));

        try {
            return batchTranCtrlMapper.updateBatchTranCtrl(batchTranCtrl) > 0;
        } catch (Exception e) {
            log.error("编辑批量交易失败", e);
            throw new BizException("编辑失败");
        }
    }
}
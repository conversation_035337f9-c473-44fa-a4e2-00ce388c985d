package com.xm.xzp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xm.xzp.model.entity.BatchTranDtl;
import com.xm.xzp.model.vo.BatchTranDtlVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface BatchTranDtlMapper extends BaseMapper<BatchTranDtl> {
    List<BatchTranDtl> selectBatchTranDtlList(@Param("query") BatchTranDtlVo query);
}
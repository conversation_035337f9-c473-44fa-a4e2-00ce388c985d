package com.xm.xzp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.model.entity.XzpOpLog;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public interface IXzpOpLogService extends IService<XzpOpLog> {

    /**
     * 分页查询日志记录
     *
     * @param xzpOpLog  页面参数
     * @param pageNum  页码
     * @param pageSize 每页多少条
     * @return IPage<XzpOpLog>
     */
    PageInfo<XzpOpLog> queryLogPage(XzpOpLog xzpOpLog, Integer pageNum, Integer pageSize);

    public int addLog(XzpOpLog xzpOpLog);

    Workbook exportLogs(XzpOpLog xzpOpLog);

    XzpOpLog getXzpLog(String id);

    XzpOpLog getLogSensitiveInfo(String id, String sensitiveStatus);
}

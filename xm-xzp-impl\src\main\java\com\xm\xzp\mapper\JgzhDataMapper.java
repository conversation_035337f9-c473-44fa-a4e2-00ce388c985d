package com.xm.xzp.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xm.xzp.model.entity.JgzhData;
import com.xm.xzp.model.entity.JgzhSpData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description:
 * @Author: Lyl
 * @Date:   2024-11-04
 * @Version: V1.0
 */
@Component
public interface JgzhDataMapper extends BaseMapper<JgzhData> {

    boolean insertJgzhData(JgzhData jgzhData);

    int updateJgzhBatchByParam(List<JgzhData> jgzhUptList);

    int updateJgzhById(JgzhData dd);

    JgzhData selectInfoById(String id);

    JgzhData selectInfoByParam(JgzhData queryData);

    List<JgzhData> queryList(JgzhData jgzhData);

    int updateJgzhByParam(JgzhData jd);

    int updateJgzhBatchByIds(@Param("upStatusList")List<JgzhData> upStatusList);
}

package com.xm.xzp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 账户信息查询参数
 * <AUTHOR>
 */
@Data
@ApiModel(value = "AcctInfoQueryVo", description = "账户信息查询参数")
public class AcctInfoQueryVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "业务代码")
    private String opeCd;

    @ApiModelProperty(value = "委托单位代码")
    private String merchId;

    @ApiModelProperty(value = "开户机构")
    private String openBrhId;

    @ApiModelProperty(value = "人行机构代码")
    private String pbcBrhId;

    @ApiModelProperty(value = "监管账号")
    private String cpabAccId;

    @ApiModelProperty(value = "账户名称")
    private String acctNm;

    @ApiModelProperty(value = "本行目标账号")
    private String peerAccId;

    @ApiModelProperty(value = "本行目标账号名称")
    private String dtlCstmNm;
}

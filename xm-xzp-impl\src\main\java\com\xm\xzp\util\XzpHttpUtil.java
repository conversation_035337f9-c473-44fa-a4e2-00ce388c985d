package com.xm.xzp.util;

import com.xm.xzp.product.security.decode.CdpDecryptUtil;
import com.xm.xzp.product.security.decode.CdpEncryptUtil;
import com.xm.xzp.product.security.exception.DecryptFailureException;
import com.xm.xzp.product.security.sign.CdpSignUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class XzpHttpUtil {

    /**
     * 生成加密加签的发起报文数据
     *
     * @param authorized 已经取得客户授权，传1，否则传0。未取得客户授权不返回数据。
     * @param idcard     身份证
     * @param name       姓名
     * @param pushurl    回调地址
     * @param secretKey  此处应替换为20个字符以内的加密Key
     * @param key        此处应替换为32个字符长度的Key
     * @return 查询结果
     */
    public  Map<String, Object> sendData(String authorized, String idcard, String name, String pushurl, String secretKey, String key) {
        try {
            Map<String, String> param = new HashMap<>(7);
            param.put("authorized", CdpEncryptUtil.aesEncrypt(authorized, secretKey));
            param.put("idcard", CdpEncryptUtil.aesEncrypt(idcard, secretKey));
            param.put("name", CdpEncryptUtil.aesEncrypt(name, secretKey));
            param.put("pushurl", CdpEncryptUtil.aesEncrypt(pushurl, secretKey));
            param.put("timestamp", CdpEncryptUtil.aesEncrypt(System.currentTimeMillis() + "", secretKey));
            String sign = CdpSignUtil.sign(param);
            Map<String, Object> params = new HashMap<>(param);
            params.put("key", key);
            params.put("sign", sign);
            return params;
        } catch (Exception e) {
            log.info(e.getMessage());
            return null;
        }
    }

    /**
     * 解密数据
     *
     * @param str
     * @param secretKey
     * @return
     */
    public  String aesDecrypt(String str, String secretKey) {
        try {
            return CdpDecryptUtil.aesDecrypt(str, secretKey);
        } catch (DecryptFailureException e) {
            log.info(e.getMessage());
            return null;
        }
    }

    /**
     * 发起请求
     *
     * @param url
     * @param params
     * @return
     * @throws Exception
     */
    public  String sendpost(String url, Map<String, Object> params) throws Exception {
        ArrayList<NameValuePair> pairs = covertParams2NVPS(params);
        return PostHttpRequest(url, pairs);
    }

    public  String PostHttpRequest(String url, List<NameValuePair> params) throws Exception {
        CloseableHttpClient client = HttpClients.createDefault();
        // 超时时间
        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(300000).setConnectTimeout(300000).build();
        String result = null;
        try {
            HttpPost request = new HttpPost(url);
            request.setConfig(requestConfig);
            request.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
            HttpResponse respones = client.execute(request);
            if (respones.getStatusLine().getStatusCode() == 200) {
                result = EntityUtils.toString(respones.getEntity(), "UTF-8");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            client.close();
        }
        return result;
    }

    private  ArrayList<NameValuePair> covertParams2NVPS(Map<String, Object> params) {
        ArrayList<NameValuePair> pairs = new ArrayList<NameValuePair>();
        if (params == null || params.size() == 0) {
            return pairs;
        }
        for (Map.Entry<String, Object> param : params.entrySet()) {
            Object value = param.getValue();
            if (value instanceof String[]) {
                String[] values = (String[]) value;
                for (String v : values) {
                    pairs.add(new BasicNameValuePair(param.getKey(), v));
                }
            } else {
                if (value instanceof Integer) {
                    value = Integer.toString((Integer) value);
                } else if (value instanceof Long) {
                    value = Long.toString((Long) value);
                }
                pairs.add(new BasicNameValuePair(param.getKey(), (String) value));
            }
        }
        return pairs;
    }
}

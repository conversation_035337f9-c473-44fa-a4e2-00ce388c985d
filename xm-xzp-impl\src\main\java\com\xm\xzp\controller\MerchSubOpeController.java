package com.xm.xzp.controller;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.api.MerchSubOpeApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.entity.MerchSubOpe;
import com.xm.xzp.model.vo.MerchSubOpeGroupVo;
import com.xm.xzp.model.vo.MerchSubOpeVo;
import com.xm.xzp.service.IMerchSubOpeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@Component
public class MerchSubOpeController implements MerchSubOpeApi {

    @Resource
    private IMerchSubOpeService merchSubOpeService;

    @Override
    @PMCTLLog(name = "查询商户子操作", action = "query")
    public RestResponse<PageInfo<MerchSubOpe>> merchSubOpeList(MerchSubOpeVo merchSubOpe,
                                                             Integer pageNum, Integer pageSize) {
        PageInfo<MerchSubOpe> pageList = merchSubOpeService.merchSubOpeList(
                merchSubOpe, pageNum, pageSize);
        return RestResponse.success(pageList);
    }

    @Override
    @PMCTLLog(name = "保存或更新商户子操作", action = "save")
    public RestResponse<Boolean> saveOrUpdateMerchSubOpe(MerchSubOpe merchSubOpe) {
        boolean result = merchSubOpeService.saveOrUpdateMerchSubOpe(merchSubOpe);
        return RestResponse.success(result);
    }

    @Override
    @PMCTLLog(name = "删除商户子操作", action = "delete")
    public RestResponse<Boolean> deleteMerchSubOpe(MerchSubOpe merchSubOpe) {
        boolean result = merchSubOpeService.deleteMerchSubOpe(merchSubOpe);
        return RestResponse.success(result);
    }

    @Override
    @PMCTLLog(name = "按子操作码分组查询列表", action = "query")
    public RestResponse<List<MerchSubOpeGroupVo>> listGroupBySubOpeId(MerchSubOpeGroupVo merchSubOpeGroupVo) {
        List<MerchSubOpeGroupVo> list = merchSubOpeService.listGroupBySubOpeId(merchSubOpeGroupVo);
        return RestResponse.success(list);
    }
}
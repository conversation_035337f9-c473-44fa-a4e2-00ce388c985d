package com.xm.xzp.util;

/**
 * <p>
 *     系统模块常量类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/3/3 18:32
 */
public class AdminConstants {

    /**
     * 默认根级ID
     */
    public static final String DEFAULT_PARENT_ID = "-1";

    /**
     * 序列拼接符.
     */
    public static final String SEPARATOR = ".";

    /**
     * 分隔符-斜线
     */
    public static final String SEPARATOR_SLASH = "/";

    /**
     *  分隔符-横线
     */
    public static final String SEPARATOR_LINE = "-";

    /**
     *  分隔符-冒号
     */
    public static final String SEPARATOR_COLON = ":";

    /**
     * 手机号脱敏正则表达式-前三后四脱敏
     */
    public static final String PHONE_NUMBER_DES_REGEX = "(\\d{3})\\d{4}(\\d{4})";

    /**
     * 手机号脱敏符号
     */
    public static final String PHONE_NUMBER_DES_SIGN = "$1****$2";

    /**
     * 身份证号脱敏正则表达式-前三后四脱敏
     */
    public static final String ID_CARD_DES_REGEX = "(?<=\\w{3})\\w(?=\\w{4})";

    /**
     * 身份证号脱敏符号
     */
    public static final String ID_CARD_DES_SIGN = "*";

    /**
     * 邮箱脱敏正则表达式
     */
    public static final String EMAIL_DES_REGEX = "(\\w+)\\w{3}@(\\w+)";

    /**
     * 邮箱脱敏符号
     */
    public static final String EMAIL_DES_SIGN = "$1***@$2";

    /**
     * 固定生成密码规则模式
     */
    public static final String PWD_RULE_MODE_FIX = "fix";

    /**
     * 基于身份证号生成密码规则模式
     */
    public static final String PWD_RULE_MODE_ID_CARD = "IDCard";
}

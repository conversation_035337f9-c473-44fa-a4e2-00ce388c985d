#!/bin/sh

PRG="$0"
saveddir=`pwd`
PFPJ_DAP_HOME=`dirname "$PRG"`/..
PFPJ_DAP_HOME=`cd "$PFPJ_DAP_HOME" && pwd`
cd "$saveddir"

export APP_NAME=PFPJ1.0.0_YOAF_SERVER
export MODE=service
export LOG_FOLDER="${PFPJ_DAP_HOME}"/logs
export PID_FOLDER="${LOG_FOLDER}"
export LOG_FILENAME="${APP_NAME}.out"

mkdir -p $LOG_FOLDER/$APP_NAME  # for pid

rm -fr $LOG_FOLDER/$LOG_FILENAME
ln -s /dev/null $LOG_FOLDER/$LOG_FILENAME             # "$LOG_FOLDER/$LOG_FILENAME is using for >> "$log_file" 2>&1"
export JAVA_OPTS="$JAVA_OPTS -Dlogging.file=$LOG_FOLDER/${APP_NAME}.log"

export JAVA_OPTS="$JAVA_OPTS -server -Djava.net.preferIPv4Stack=true -Duser.timezone=Asia/Shanghai -Dclient.encoding.override=UTF-8 -Dfile.encoding=UTF-8 -Djava.security.egd=file:/dev/./urandom"
export JAVA_OPTS="$JAVA_OPTS $PFPJ_DAP_HEAP_OPTS"
export JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
# export JAVA_OPTS="$JAVA_OPTS -XX:+UseParNewGC -XX:ParallelGCThreads=4 -XX:MaxTenuringThreshold=9 -XX:+UseConcMarkSweepGC"

action=$1
if [[ "$action" == "run" ]] || [[ "$action" == "start" ]]; then
  shift
else
  action="start"
fi
BOOT_JAR=`echo "${PFPJ_DAP_HOME}"/*.jar`
$BOOT_JAR $action "$@"


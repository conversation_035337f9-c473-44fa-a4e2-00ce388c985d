<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xm.xzp.mapper.BatchProcessLogMapper">
    <select id="batchProcessLogList" resultType="com.xm.xzp.model.entity.BatchProcessLog">
        select
        tran_dt,
        merch_id,
        ope_cd,
        merch_name,
        proc_memo,
        in_file_name,
        out_file_name,
        batch_id,
        tol_num,
        tol_amt,
        act_num,
        act_amt,
        proc_flag,
        proc_reason,
        load_time
        from tb_batch_process_log
        where 1=1
        <if test="startTime != null and endTime !=''">
            and tran_dt &gt;= #{startTime}
            and tran_dt &lt;= #{endTime}
        </if>
        <if test="merchId != null and merchId != ''">
            and merch_id = #{merchId}
        </if>
        <if test="opeCd != null and opeCd != ''">
            and ope_cd = #{opeCd}
        </if>
        <if test="batchId != null and batchId != ''">
            and batch_id = #{batchId}
        </if>
        order by load_time desc
    </select>
</mapper>
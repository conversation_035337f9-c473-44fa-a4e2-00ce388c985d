package com.xm.xzp.api;

import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.entity.MerchSubOpe;
import com.xm.xzp.model.vo.MerchSubOpeGroupVo;
import com.xm.xzp.model.vo.MerchSubOpeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@ResponseBody
@RequestMapping("/api/admin/xzp/merch")
@Api(tags = "商户子操作管理")
@Validated
public interface MerchSubOpeApi {

    @ApiOperation(value = "商户子操作查询", notes = "merchSubOpeList")
    @PostMapping("/merchSubOpeList")
    RestResponse<PageInfo<MerchSubOpe>> merchSubOpeList(
            @RequestBody MerchSubOpeVo merchSubOpe,
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize);

    @ApiOperation(value = "保存或更新商户子操作", notes = "saveOrUpdateMerchSubOpe")
    @PostMapping("/saveOrUpdateMerchSubOpe")
    RestResponse<Boolean> saveOrUpdateMerchSubOpe(@RequestBody MerchSubOpe merchSubOpe);

    @ApiOperation(value = "删除商户子操作", notes = "deleteMerchSubOpe")
    @PostMapping("/deleteMerchSubOpe")
    RestResponse<Boolean> deleteMerchSubOpe(@RequestBody MerchSubOpe merchSubOpe);

    @ApiOperation(value = "按子操作码分组查询列表", notes = "listGroupBySubOpeId")
    @PostMapping("/listGroupBySubOpeId")
    RestResponse<List<MerchSubOpeGroupVo>> listGroupBySubOpeId(@RequestBody MerchSubOpeGroupVo merchSubOpeGroupVo);
}
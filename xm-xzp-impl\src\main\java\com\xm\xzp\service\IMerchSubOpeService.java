package com.xm.xzp.service;

import com.github.pagehelper.PageInfo;
import com.xm.xzp.model.entity.MerchSubOpe;
import com.xm.xzp.model.vo.MerchSubOpeGroupVo;
import com.xm.xzp.model.vo.MerchSubOpeVo;

import java.util.List;

public interface IMerchSubOpeService {
    PageInfo<MerchSubOpe> merchSubOpeList(MerchSubOpeVo merchSubOpe, Integer pageNum, Integer pageSize);
    boolean saveOrUpdateMerchSubOpe(MerchSubOpe merchSubOpe);
    boolean deleteMerchSubOpe(MerchSubOpe merchSubOpe);
    List<MerchSubOpeGroupVo> listGroupBySubOpeId(MerchSubOpeGroupVo merchSubOpeGroupVo);
}
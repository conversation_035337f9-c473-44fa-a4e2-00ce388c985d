package com.xm.xzp.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.mapper.BatchProcessLogMapper;
import com.xm.xzp.model.entity.BatchProcessLog;
import com.xm.xzp.model.entity.BatchProcessLog;
import com.xm.xzp.model.vo.BatchProcessLogVo;
import com.xm.xzp.service.IBatchProcessLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.awt.geom.QuadCurve2D;
import java.util.List;

@Service
@Slf4j
public class BatchProcessLogServiceImpl extends ServiceImpl<BatchProcessLogMapper, BatchProcessLog>
        implements IBatchProcessLogService {

    @Resource
    private BatchProcessLogMapper batchProcessLogMapper;

    @Override
    @DS("datasource2")
    public PageInfo<BatchProcessLog> batchProcessLogList(BatchProcessLogVo batchProcessLog, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<BatchProcessLog> list = this.list(getBatchProcessLogQueryWrapper(batchProcessLog));
        return new PageInfo<>(list);
    }

    private QueryWrapper<BatchProcessLog> getBatchProcessLogQueryWrapper(BatchProcessLogVo batchProcessLog) {
        QueryWrapper<BatchProcessLog> queryWrapper = new QueryWrapper<>();
        if (batchProcessLog != null) {
            LambdaQueryWrapper<BatchProcessLog> lambdaQueryWrapper = queryWrapper.lambda();
            if ( StringUtils.isNotBlank(batchProcessLog.getStartTime()) && StringUtils.isNotBlank(batchProcessLog.getEndTime())) {
                lambdaQueryWrapper.le(BatchProcessLog::getTranDt, batchProcessLog.getEndTime());
                lambdaQueryWrapper.ge(BatchProcessLog::getTranDt, batchProcessLog.getStartTime());
            }
            if (StringUtils.isNotBlank(batchProcessLog.getMerchId())) {
                lambdaQueryWrapper.eq(BatchProcessLog::getMerchId, batchProcessLog.getMerchId());
            }
            if (StringUtils.isNotBlank(batchProcessLog.getOpeCd())) {
                lambdaQueryWrapper.eq(BatchProcessLog::getOpeCd, batchProcessLog.getOpeCd());
            }
            if (StringUtils.isNotBlank(batchProcessLog.getBatchId())) {
                lambdaQueryWrapper.eq(BatchProcessLog::getBatchId, batchProcessLog.getBatchId());
            }
        }
        queryWrapper.orderByDesc("tran_dt");
        return queryWrapper;
    }
}
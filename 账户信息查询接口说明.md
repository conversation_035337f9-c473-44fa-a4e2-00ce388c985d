# 账户信息查询接口说明

## 接口概述

本接口用于根据户名或公司账户进行模糊查询，返回资金监管账户信息列表，主要用于前端下拉控件的联想功能。当关键字为空时，返回全量数据。接口返回数据使用 `RestResponse` 实体包裹，遵循项目统一的响应格式。

## 接口信息

- **接口路径**: `/api/admin/xzp/suggestAcctInfo`
- **请求方式**: `GET`
- **数据源**: `datasource2` (PostgreSQL数据库)
- **表名**: `public.tb_zjjg_acct_info`
- **返回格式**: `RestResponse<List<TbZjjgAcctInfo>>`

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | String | 否 | 查询关键字，支持户名(acct_nm)或公司账户(cpab_acc_id)模糊匹配，为空时返回全量数据 |

## 响应数据

返回 `RestResponse<List<TbZjjgAcctInfo>>` 格式的JSON对象，其中 `data` 字段包含账户信息数组，每个账户信息包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| merchId | String | 委托单位代码 |
| opeCd | String | 业务代码 |
| transType | String | 业务类型 |
| cpabAccId | String | 公司账户 |
| openBrhId | String | 开户机构号 |
| brhName | String | 开户机构名称 |
| pbcBrhId | String | 人行机构号 |
| acctNm | String | 户名 |
| tranDt | String | 交易日期 |
| paperType | String | 证件类型 |
| paperId | String | 证件号码 |
| peerAccId | String | 关联账号 |
| dtlCstmNm | String | 关联户名 |
| acctType | String | 关联账户类型（1-同行，2-跨行） |
| txnBrhId | String | 交易机构 |
| tranTime | String | 交易时间戳 |
| tlrId | String | 操作员 |
| othMsg1Tx | String | 附加字段1 |
| othMsg2Tx | String | 附加字段2 |
| othMsg3Tx | String | 附加字段3 |
| othMsg4Tx | String | 附加字段4 |
| othMsg5Tx | String | 附加字段5 |

## 请求示例

```bash
# 根据关键字查询
GET /api/admin/xzp/suggestAcctInfo?keyword=张三
```

```bash
# 根据账户查询
GET /api/admin/xzp/suggestAcctInfo?keyword=1234567890
```

```bash
# 查询全量数据
GET /api/admin/xzp/suggestAcctInfo
```

## 响应示例

```json
{
  "code": "200",
  "message": "操作成功",
  "data": [
    {
      "merchId": "001",
      "opeCd": "ZZG001",
      "transType": "zzg",
      "cpabAccId": "1234567890123456",
      "openBrhId": "001001",
      "brhName": "厦门分行",
      "pbcBrhId": "102100000001",
      "acctNm": "张三",
      "tranDt": "20241225",
      "paperType": "01",
      "paperId": "350100199001011234",
      "peerAccId": "9876543210987654",
      "dtlCstmNm": "李四",
      "acctType": "1",
      "txnBrhId": "001001001",
      "tranTime": "20241225143000",
      "tlrId": "001",
      "othMsg1Tx": null,
      "othMsg2Tx": null,
      "othMsg3Tx": null,
      "othMsg4Tx": null,
      "othMsg5Tx": null
    }
  ],
  "success": true
}
```

## 查询逻辑

1. 接口支持根据 `acct_nm`（户名）或 `cpab_acc_id`（公司账户）进行模糊匹配
2. 使用 `LIKE '%keyword%'` 的方式进行模糊查询
3. 结果按 `acct_nm`, `cpab_acc_id` 排序
4. 当有关键字时，限制返回最多100条记录，避免数据量过大
5. 当关键字为空时，返回全量数据（不限制条数）

## 技术实现

### 文件结构

1. **实体类**: `TbZjjgAcctInfo.java` - 对应数据库表结构
2. **API接口**: `AcctInfoApi.java` - 定义接口规范
3. **Mapper接口**: `TbZjjgAcctInfoMapper.java` - 数据访问层，使用 `@DS("datasource2")` 指定数据源
4. **Mapper XML**: `TbZjjgAcctInfoMapper.xml` - SQL查询语句
5. **Service接口**: `ITbZjjgAcctInfoService.java` - 业务逻辑接口
6. **Service实现**: `TbZjjgAcctInfoServiceImpl.java` - 业务逻辑实现
7. **Controller**: `AcctInfoController.java` - 控制器实现

### 关键特性

- 使用 `@DS("datasource2")` 注解指定使用第二个数据源
- 继承 MyBatis-Plus 的 `BaseMapper` 和 `IService`，提供基础CRUD功能
- 包含完整的异常处理和日志记录
- 支持 Swagger API 文档自动生成

## 注意事项

1. 确保 `datasource2` 数据源配置正确
2. 确保数据库中存在 `public.tb_zjjg_acct_info` 表
3. 建议在生产环境中对查询结果进行缓存优化
4. 可根据实际需求调整返回字段和查询条件
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xm.xzp.mapper.MerchOpeMapper">

    <select id="selectGroupByMerchId" resultType="com.xm.xzp.model.vo.MerchOpeGroupVo">
        SELECT
        merch_id,
        MAX(prdt_nm) as prdt_nm,
        MAX(merch_ope_on_off_fg) as merch_ope_on_off_fg,
        MAX(merch_ope_type) as merch_ope_type,
        COUNT(1) as count
        FROM
        tb_merch_ope
        <where>
            <if test="merchId != null and merchId != ''">
                AND merch_id LIKE CONCAT('%', #{merchId}, '%')
            </if>
            <if test="opeCd != null and opeCd != ''">
                AND ope_cd = #{opeCd}
            </if>
            <if test="prdtNm != null and prdtNm != ''">
                AND prdt_nm LIKE CONCAT('%', #{prdtNm}, '%')
            </if>
            <if test="merchOpeOnOffFg != null and merchOpeOnOffFg != ''">
                AND merch_ope_on_off_fg = #{merchOpeOnOffFg}
            </if>
            <if test="merchOpeType != null and merchOpeType != ''">
                AND merch_ope_type = #{merchOpeType}
            </if>
            <if test="openDtStart != null and openDtStart != ''">
                AND open_dt &gt;= #{openDtStart}
            </if>
            <if test="openDtEnd != null and openDtEnd != ''">
                AND open_dt &lt;= #{openDtEnd}
            </if>
        </where>
        GROUP BY
        merch_id
        ORDER BY
        merch_id
    </select>

    <select id="selectGroupByOpeCd" resultType="com.xm.xzp.model.entity.OpeCd">
        SELECT
        ope_cd,
        ope_nm
        FROM
        tb_ope_cd
        <where>
            <if test="opeCd != null and opeCd != ''">
                AND ope_cd LIKE CONCAT('%', #{opeCd}, '%')
            </if>
        </where>
        ORDER BY
        update_tm DESC
    </select>
</mapper>
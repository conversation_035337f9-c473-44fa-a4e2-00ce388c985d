package com.xm.xzp.product.security.exception;


public class SignFailureException extends Exception {
    private String code = "-1";
    private String msg;

    public SignFailureException(String msg) {
        this.msg = msg;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}

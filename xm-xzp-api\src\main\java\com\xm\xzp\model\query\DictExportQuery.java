package com.xm.xzp.model.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 字典导出查询参数
 */
@Data
public class DictExportQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典类型代码
     */
    private String dictTypeId;

    /**
     * 字典类型名称
     */
    private String dictTypeName;

    /**
     * 状态 1-启用 2-停用
     */
    private String status;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 需要导出的字典类型
     */
    private List<String> dictTypeIds;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xm.xzp.mapper.BatchTranCtrlMapper">
    <select id="batchTranCtrlList"  resultType="com.xm.xzp.model.entity.BatchTranCtrl">
        select
            batch_id,
            core_batch_id,
            tran_dt,
            plan_tran_dt,
            tran_inst_id,
            node_type,
            tran_cd,
            batch_req_tran_cd,
            merch_id,
            ope_cd,
            total_qt,
            total_at,
            succ_qt,
            succ_at,
            fail_qt,
            fail_at,
            exec_dt,
            file_inacc_dt,
            tlr_id,
            auth_tlr_id,
            inst_term_id,
            req_file_name_tx,
            req_file_dt,
            sta_file_name_i,
            sta_file_name_o,
            rsp_file_name,
            oth_msg1_tx,
            oth_msg2_tx,
            oth_msg3_tx,
            chnl_cd,
            jnl_cd,
            proc_fg,
            ans_cd,
            ans_tx,
            file_nm,
            last_modify_prg_id,
            last_modify_tlr_id,
            last_modify_tm
        from tb_batch_tran_ctrl
        where 1=1
        <if test="startTime != null and endTime !=''">
            and tran_dt &gt;= #{startTime}
            and tran_dt &lt;= #{endTime}
        </if>
        <if test="merchId != null and merchId != ''">
            and merch_id = #{merchId}
        </if>
        <if test="opeCd != null and opeCd != ''">
            and ope_cd = #{opeCd}
        </if>
        <if test="procFg != null and procFg != ''">
            and proc_fg = #{procFg}
        </if>
        order by tran_dt desc
    </select>

    <update id="updateBatchTranCtrl" parameterType="com.xm.xzp.model.entity.BatchTranCtrl">
        update tb_batch_tran_ctrl
        <set>
            <if test="coreBatchId != null">core_batch_id = #{coreBatchId},</if>
            <if test="tranDt != null">tran_dt = #{tranDt},</if>
            <if test="planTranDt != null">plan_tran_dt = #{planTranDt},</if>
            <if test="tranInstId != null">tran_inst_id = #{tranInstId},</if>
            <if test="nodeType != null">node_type = #{nodeType},</if>
            <if test="tranCd != null">tran_cd = #{tranCd},</if>
            <if test="batchReqTranCd != null">batch_req_tran_cd = #{batchReqTranCd},</if>
            <if test="totalQt != null">total_qt = #{totalQt},</if>
            <if test="totalAt != null">total_at = #{totalAt},</if>
            <if test="succQt != null">succ_qt = #{succQt},</if>
            <if test="succAt != null">succ_at = #{succAt},</if>
            <if test="failQt != null">fail_qt = #{failQt},</if>
            <if test="failAt != null">fail_at = #{failAt},</if>
            <if test="execDt != null">exec_dt = #{execDt},</if>
            <if test="fileInaccDt != null">file_inacc_dt = #{fileInaccDt},</if>
            <if test="tlrId != null">tlr_id = #{tlrId},</if>
            <if test="authTlrId != null">auth_tlr_id = #{authTlrId},</if>
            <if test="instTermId != null">inst_term_id = #{instTermId},</if>
            <if test="reqFileNameTx != null">req_file_name_tx = #{reqFileNameTx},</if>
            <if test="reqFileDt != null">req_file_dt = #{reqFileDt},</if>
            <if test="staFileNameI != null">sta_file_name_i = #{staFileNameI},</if>
            <if test="staFileNameO != null">sta_file_name_o = #{staFileNameO},</if>
            <if test="rspFileName != null">rsp_file_name = #{rspFileName},</if>
            <if test="othMsg1Tx != null">oth_msg1_tx = #{othMsg1Tx},</if>
            <if test="othMsg2Tx != null">oth_msg2_tx = #{othMsg2Tx},</if>
            <if test="othMsg3Tx != null">oth_msg3_tx = #{othMsg3Tx},</if>
            <if test="chnlCd != null">chnl_cd = #{chnlCd},</if>
            <if test="jnlCd != null">jnl_cd = #{jnlCd},</if>
            <if test="procFg != null">proc_fg = #{procFg},</if>
            <if test="ansCd != null">ans_cd = #{ansCd},</if>
            <if test="ansTx != null">ans_tx = #{ansTx},</if>
            <if test="fileNm != null">file_nm = #{fileNm},</if>
            last_modify_prg_id = #{lastModifyPrgId},
            last_modify_tlr_id = #{lastModifyTlrId},
            last_modify_tm = #{lastModifyTm}
        </set>
        where batch_id = #{batchId}
        and merch_id = #{merchId}
        and ope_cd = #{opeCd}
    </update>
</mapper>
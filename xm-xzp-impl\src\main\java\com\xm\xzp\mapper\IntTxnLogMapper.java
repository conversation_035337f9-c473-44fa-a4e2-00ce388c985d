package com.xm.xzp.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xm.xzp.model.entity.IntTxnLog;
import com.xm.xzp.model.vo.IntTxnLogResultVo;
import com.xm.xzp.model.vo.IntTxnLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("datasource2")
public interface IntTxnLogMapper extends BaseMapper<IntTxnLog> {
    /**
     * 查询交易流水列表（关联商户操作表、操作码表和商户子操作表）
     *
     * @param query 查询条件
     * @return 交易流水列表
     */
    List<IntTxnLogResultVo> selectIntTxnLogList(@Param("query") IntTxnLogVo query);
}
package com.xm.xzp.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.context.util.UserInfoContext;
import com.xm.xzp.enums.SensitiveStatusEnum;
import com.xm.xzp.mapper.JgzhDataMapper;
import com.xm.xzp.mapper.JgzhSpDataMapper;
import com.xm.xzp.model.entity.JgzhData;
import com.xm.xzp.model.entity.JgzhSpData;
import com.xm.xzp.model.entity.XzpOpLog;
import com.xm.xzp.service.IJgzhShDataService;
import com.xm.xzp.service.IXzpOpLogService;
import com.xm.xzp.util.AdminConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class JgzhSpDataServiceImpl extends ServiceImpl<JgzhSpDataMapper, JgzhSpData> implements IJgzhShDataService {

    @Resource
    private JgzhSpDataMapper jgzhSpDataMapper;
    @Resource
    private JgzhDataMapper jgzhDataMapper;

    @Resource
    private IXzpOpLogService xzpOpLogService;

    /**
     * 查询监管账户变动情况反馈审核列表
     * **/
    @Override
    public PageInfo<JgzhSpData> querySpPage(JgzhSpData jgzhSpData, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<JgzhSpData> list = jgzhSpDataMapper.queryList(jgzhSpData);
        if (!list.isEmpty()) {
            System.out.println("查询监管账户条数-->"+list.size());
        }
        // 默认隐藏信息
        for (JgzhSpData em : list) {
            String accountno = em.getAccountno();
            String accountname = em.getAccountname();
            if (StringUtils.isNotEmpty(accountno)) {
                accountno = accountno.replaceAll(AdminConstants.ID_CARD_DES_REGEX, AdminConstants.ID_CARD_DES_SIGN);
                em.setAccountno(accountno);
            }

            if (StringUtils.isNotEmpty(accountname)) {
                accountname = accountname.replaceAll("(?<=.{1}).", "*");
                em.setAccountname(accountname);
            }
        }
        //记录日志
        XzpOpLog xzpOpLog = new XzpOpLog();
        xzpOpLog.setStepId("查询");
        xzpOpLog.setStepName("查询监管账户被执行反馈审核信息记录");
        xzpOpLog.setDealRes("成功");
        xzpOpLog.setOpinion("查询监管账户被执行反馈审核信息记录成功："+list.size()+"条");
        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值
        try{
            userName = UserInfoContext.currentUserName();
        }catch(Exception e){
            log.error("[querySpPage]获取用户名异常，"+e);
        }
        xzpOpLog.setDealUser(userName);
        xzpOpLog.setCreateBy(userName);
        xzpOpLog.setStartTime(dt);
        xzpOpLog.setCreateTime(dt);
        xzpOpLog.setApplyType("jgzh");
        xzpOpLog.setFlowId("xzp_sp_info");
        xzpOpLogService.addLog(xzpOpLog);
        return new PageInfo<>(list);
    }

    /**
     * 插入监管账户变动情况反馈审核表,新增日志
     * */
    @Override
    public int addSh(JgzhData jgzhData) {
        log.info("新增监管账户被执行变动情况审核记录:[{}]", "");
       // log.info("新增监管账户被执行变动情况审核记录:[{}]", jgzhData);
        //记录日志
        XzpOpLog xzpOpLog = new XzpOpLog();
        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值
        try{
            userName = UserInfoContext.currentUserName();
        }catch(Exception e){
            log.error("[querySpPage]获取用户名异常，"+e);
        }
        try{
            jgzhData.setCreateTime(dt);//创建时间
            jgzhData.setCreateBy(userName);//创建人
            boolean a = jgzhSpDataMapper.insertJgzhSpData(jgzhData);
            if(a){
                log.info("新增监管账户被执行变动情况信息审核记录成功");
            }
            xzpOpLog.setDealRes("成功");
            xzpOpLog.setOpinion("新增监管账户被执行反馈审核信息记录入库xzp_sp_info成功");
        }catch (Exception e){
            xzpOpLog.setDealRes("失败");
            xzpOpLog.setOpinion("新增监管账户被执行反馈审核信息记录入库xzp_sp_info失败");
            log.error("新增监管账户项目审核记录异常：{}", e.getMessage());
        }finally {
            xzpOpLog.setStepId("查询");
            xzpOpLog.setStepName("新增监管账户被执行反馈审核信息记录入库");
            xzpOpLog.setDealUser(userName);
            xzpOpLog.setCreateBy(userName);
            xzpOpLog.setStartTime(dt);
            xzpOpLog.setCreateTime(dt);
            xzpOpLog.setApplyType("jgzh");
            xzpOpLog.setFlowId("xzp_sp_info");
            xzpOpLogService.addLog(xzpOpLog);
        }
        return 1;
    }

    /**
     * 监管账户变动情况反馈审核-单个
     * */
    @Override
    public boolean spJgzhInfo(JgzhSpData jgzhSpData) {
            //log.info("审核信息:[{}]", jgzhSpData.toString());
            //记录日志
            XzpOpLog xzpOpLog = new XzpOpLog();
            xzpOpLog.setStepId("审核");
            xzpOpLog.setStepName("监管账户变动情况反馈审核");

            String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String userName ="default";//异常情况下工号使用默认值
            try{
                userName = UserInfoContext.currentUserName();
            }catch(Exception e){
                log.error("[spJgzhInfo]获取用户名异常，"+e);
            }
            xzpOpLog.setDealUser(userName);
            xzpOpLog.setCreateBy(userName);
            xzpOpLog.setStartTime(dt);
            xzpOpLog.setCreateTime(dt);
            xzpOpLog.setApplyType("jgzh");
            xzpOpLog.setFlowId("xzp_sp_info");
            xzpOpLog.setSpeReleId(jgzhSpData.getId());

            JgzhSpData dd = jgzhSpData;
            dd.setUpdateTime(dt);//更新时间
            dd.setUpdateBy(userName);//更新人
            dd.setSpBy(userName);
            dd.setSpTime(dt);
            dd.setSpClassification("jgzh");
            dd.setSpStatus("1");//1-已审核
            int a = jgzhSpDataMapper.updateById(dd);
            if(a>0){
                xzpOpLog.setDealRes("成功");
                xzpOpLog.setOpinion("监管账户变动情况反馈审核成功");
                //查询申请详情
                JgzhSpData latestSp = jgzhSpDataMapper.selectInfoById(jgzhSpData.getId());
                //更新申请表状态
                JgzhData jd  = new JgzhData();
                jd.setJgzhStatus(jgzhSpData.getSpRes());//1-审核通过，待反馈，2-审核不通过
              //  jd.setSerialno(jgzhSpData.getSerialno());//执行流水，根据执行流水号来修改数据
                jd.setId(latestSp.getApplicationId());//根据id更新数据
              //  log.info("审核完成后修改监管账户信息："+jd.toString());
               // jgzhDataMapper.updateJgzhByParam(jd);
                jgzhDataMapper.updateJgzhById(jd);
                //记录操作日志
                xzpOpLogService.addLog(xzpOpLog);
                return true;
            }else{
                xzpOpLog.setDealRes("失败");
                xzpOpLog.setOpinion("监管账户变动情况反馈审核失败，无审核记录");
                xzpOpLogService.addLog(xzpOpLog);
                return false;
            }
    }

    /**
     * 获取监管账户变动情况反馈审核详情
     * */
    @Override
    public JgzhSpData getJgzhSpDetail(String id) {
        log.info("查询监管账户变动情况反馈审核信息:[id:{}]", id);
        JgzhSpData jgzhSpData = jgzhSpDataMapper.selectInfoById(id);
        //记录日志
        XzpOpLog xzpOpLog = new XzpOpLog();
        xzpOpLog.setStepId("查询");
        xzpOpLog.setStepName("查询监管账户被执行反馈审核记录详情");
        xzpOpLog.setDealRes("成功");
        xzpOpLog.setOpinion("查询监管账户被执行反馈审核记录详情成功");
        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值
        try{
            userName = UserInfoContext.currentUserName();
        }catch(Exception e){
            log.error("[getJgzhSpDetail]获取用户名异常，"+e);
        }
        xzpOpLog.setDealUser(userName);
        xzpOpLog.setCreateBy(userName);
        xzpOpLog.setStartTime(dt);
        xzpOpLog.setCreateTime(dt);
        xzpOpLog.setApplyType("jgzh");
        xzpOpLog.setFlowId("xzp_sp_info");
        xzpOpLogService.addLog(xzpOpLog);
        return jgzhSpData;
    }

    /**
     * 监管账户变动情况反馈批量审核
     * */
    @Override
    public int batchUpdateSpRes(JgzhSpData jgzhSpData) {
        //记录日志
        XzpOpLog xzpOpLog = new XzpOpLog();
        xzpOpLog.setStepId("批量审核");
        xzpOpLog.setStepName("批量审核监管账户被执行反馈信息");
        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值
        try{
            userName = UserInfoContext.currentUserName();
        }catch(Exception e){
            log.error("[batchUpdateSpRes]获取用户名异常，"+e);
        }
        xzpOpLog.setDealUser(userName);
        xzpOpLog.setCreateBy(userName);
        xzpOpLog.setStartTime(dt);
        xzpOpLog.setCreateTime(dt);
        xzpOpLog.setApplyType("jgzh");
        xzpOpLog.setFlowId("xzp_sp_info");

        //id list
        String[] ids = jgzhSpData.getId().split(",");
        List list = new ArrayList();
        for(String id:ids){
            list.add(id);
        }
        List<JgzhSpData> spList = new ArrayList<>();

        JgzhSpData newInfo = new JgzhSpData();
        newInfo.setSpRes(jgzhSpData.getSpRes());
        newInfo.setSpContent(jgzhSpData.getSpContent());
        // 更新人
        newInfo.setUpdateBy(userName);
        // 更新时间
        newInfo.setUpdateTime(dt);
        newInfo.setId(jgzhSpData.getId());
        newInfo.setJgzhSpList(list);
        spList.add(newInfo);

       // log.info("传入的spList:"+spList);
        int update = jgzhSpDataMapper.updateBatchSpRes(spList);

        //批量修改申请表的申请状态
        List<JgzhData> upStatusList = new ArrayList<>();
        String[] applyIds = jgzhSpData.getApplicationId().split(",");
        List applyList = new ArrayList();
        for(String id:applyIds){
            applyList.add(id);
        }
        JgzhData statusInfo = new JgzhData();
        statusInfo.setJgzhStatus(jgzhSpData.getSpRes());//将状态更新为1-审核通过，待反馈；2-审核不通过
        statusInfo.setJgzhList(applyList);
        statusInfo.setId(jgzhSpData.getApplicationId());
        upStatusList.add(statusInfo);
       // log.info("传入的upStatusList:"+upStatusList);
        //updateJgzhBatchByIds方法目前就此处引用，注意限制了状态条件，状态为待反馈，反馈成功的不可更改
        // 如果前面批量审批不限制则需要将此处的限制也去除
        int updateApply = jgzhDataMapper.updateJgzhBatchByIds(upStatusList);
        log.info("批量审核结束，同步修改"+updateApply+"条申请记录状态jgzhStatus为"+jgzhSpData.getSpRes());
        xzpOpLog.setDealRes("成功");
        xzpOpLog.setOpinion("查询监管账户被执行反馈审核记录详情成功");
        xzpOpLogService.addLog(xzpOpLog);
        return update;
    }

    @Override
    public JgzhSpData getJgzhSpSensitiveInfo(String id, String sensitiveStatus) {
        log.info("显示隐藏监管账户审核敏感信息:[id:{}, sensitiveStatus:{}]", id, sensitiveStatus);

//        // 查询人员信息
//        LambdaQueryWrapper<JgzhSpData> queryWrapper = new LambdaQueryWrapper<>();
//        // 设置查询字段
//        queryWrapper.select(JgzhSpData::getAccountno,JgzhSpData::getAccountname);
//        queryWrapper.eq(JgzhSpData::getId, id);
//        JgzhSpData em = jgzhSpDataMapper.selectOne(queryWrapper);
        JgzhSpData queryParam = new JgzhSpData();
        queryParam.setId(id);
       // queryParam.setSensitiveStatus(sensitiveStatus);
        JgzhSpData em = jgzhSpDataMapper.selectOneById(queryParam);
        // 组装用户返回信息
        JgzhSpData sensitiveInfoVO = new JgzhSpData();
        BeanUtils.copyProperties(em, sensitiveInfoVO);
        // 设置脱敏
        if (SensitiveStatusEnum.UN_SENSITIVE_STATUS.statusCode().equals(sensitiveStatus)) {
            sensitiveInfoVO.setSensitiveStatus(SensitiveStatusEnum.SENSITIVE_STATUS.statusCode());

            String accountno = sensitiveInfoVO.getAccountno();
            if (StringUtils.isNotEmpty(accountno)) {
                accountno = accountno.replaceAll(AdminConstants.ID_CARD_DES_REGEX, AdminConstants.ID_CARD_DES_SIGN);
                sensitiveInfoVO.setAccountno(accountno);
            }

            String accountname = sensitiveInfoVO.getAccountname();
            if (StringUtils.isNotEmpty(accountname)) {
                accountname = accountname.replaceAll("(?<=.{1}).", "*");
                sensitiveInfoVO.setAccountname(accountname);
            }
        } else {
            // 设置未脱敏
            sensitiveInfoVO.setSensitiveStatus(SensitiveStatusEnum.UN_SENSITIVE_STATUS.statusCode());
        }

        return sensitiveInfoVO;
    }


}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xm.xzp.mapper.JgzhDataMapper">
    <!-- 根据id 查询监管账户信息-->
    <select id="selectInfoById" parameterType="java.lang.String" resultType="com.xm.xzp.model.entity.JgzhData">
         select
            id,
            serialno,
            bankid,
            accountname,
            accountno,
            executetype,
            executeamount,
            executedept,
            TO_CHAR(TO_TIMESTAMP(executedate, 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"'), 'YYYY-MM-DD HH24:MI:SS')  as executedate,
            releaseserialno,
            TO_CHAR(TO_TIMESTAMP(releasetime, 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"'), 'YYYY-MM-DD HH24:MI:SS')  as releasetime,
            note
            ,data_source
           ,jgzh_status
           ,jgzh_desc
           ,create_time
           ,create_by
           ,update_time
           ,update_by

        from xzp_jgzh_info
        where id = #{id}
    </select>

    <!-- 根据参数 查询监管账户信息-->
    <select id="selectInfoByParam" resultType="com.xm.xzp.model.entity.JgzhData">
         select
            id,
            serialno,
            bankid,
            accountname,
            accountno,
            executetype,
            executeamount,
            executedept,
            TO_CHAR(TO_TIMESTAMP(executedate, 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"'), 'YYYY-MM-DD HH24:MI:SS')  as executedate,
            releaseserialno,
            TO_CHAR(TO_TIMESTAMP(releasetime, 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"'), 'YYYY-MM-DD HH24:MI:SS')  as releasetime,
            note
            ,data_source
            ,jgzh_status
            ,jgzh_desc
           ,create_time
           ,create_by
           ,update_time
           ,update_by

        from xzp_jgzh_info
        where   serialno  =  #{serialno}
        <if test="bankid !=null and bankid != ''">
          and  bankid  =  #{bankid}
        </if>
        <if test="accountname !=null and accountname != ''">
            and  accountname =  #{accountname}
        </if>
        <if test="accountno !=null and accountno != ''">
            and  accountno =  #{accountno}
        </if>
        <if test="executetype !=null and executetype != ''">
            and  executetype =  #{executetype}
        </if>
        <if test="executeamount !=null and executeamount != ''">
            and  executeamount = #{executeamount}
        </if>
        <if test="executedept !=null and executedept != ''">
            and  executedept =  #{executedept}
        </if>
        <if test="executedate !=null and executedate != ''">
            and  executedate =  #{executedate}
        </if>
        <if test="releaseserialno !=null and releaseserialno != ''">
            and  releaseserialno =  #{releaseserialno}
        </if>
        <if test="releasetime !=null and releasetime != ''">
            and  releasetime =  #{releasetime}
        </if>
        <if test="note !=null and note != ''">
            and  note =  #{note}
        </if>

    </select>

    <!-- 分页查询监管账户导出列表 -->
    <select id="selectExportJgzh" resultType="com.xm.xzp.model.vo.ExcelJgzhVO">
        select id,
            serialno,
            bankid,
            accountname,
            accountno,
            executetype,
            executeamount,
            executedept,
            TO_CHAR(TO_TIMESTAMP(executedate, 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"'), 'YYYY-MM-DD HH24:MI:SS')  as executedate,
            releaseserialno,
            TO_CHAR(TO_TIMESTAMP(releasetime, 'YYYY-MM-DD"T"HH24:MI:SS.US"Z"'), 'YYYY-MM-DD HH24:MI:SS')  as releasetime,
            note
            ,data_source
            ,jgzh_status
            ,jgzh_desc
           ,create_time
           ,create_by
           ,update_time
           ,update_by
        from xzp_jgzh_info
        order by serialno desc
    </select>

    <update id="updateJgzhByParam" parameterType="com.xm.xzp.model.entity.JgzhData">
        update xzp_jgzh_info
        set
        <if test="bankid !=null and bankid != ''">
            bankid  =  #{bankid},
        </if>
        <if test="accountname !=null and accountname != ''">
            accountname =  #{accountname},
        </if>
        <if test="accountno !=null and accountno != ''">
            accountno =  #{accountno},
        </if>
        <if test="executetype !=null and executetype != ''">
            executetype =  #{executetype},
        </if>
        <if test="executeamount !=null and executeamount != ''">
            executeamount = #{executeamount},
        </if>
        <if test="executedept !=null and executedept != ''">
            executedept =  #{executedept},
        </if>
        <if test="executedate !=null and executedate != ''">
            executedate =  #{executedate},
        </if>
        <if test="releaseserialno !=null and releaseserialno != ''">
            releaseserialno =  #{releaseserialno},
        </if>
        <if test="releasetime !=null and releasetime != ''">
            releasetime =  #{releasetime},
        </if>
        <if test="note !=null and note != ''">
            note =  #{note},
        </if>
        <if test="jgzhStatus !=null and jgzhStatus != ''">
            jgzh_status =  #{jgzhStatus},
        </if>
        <if test="jgzhDesc !=null and jgzhDesc != ''">
            jgzh_desc =  #{jgzhDesc},
        </if>
        update_by = #{updateBy},
        update_time = #{updateTime}
        where
        serialno = #{serialno}
    </update>

    <!-- 更新信息 executeamount = to_number(#{executeamount},'**************.99'),-->
    <update id="updateJgzhById" parameterType="com.xm.xzp.model.entity.JgzhData">
        update xzp_jgzh_info
        set
        <if test="serialno !=null and serialno != ''">
            serialno  =  #{serialno},
        </if>
        <if test="bankid !=null and bankid != ''">
            bankid  =  #{bankid},
        </if>
        <if test="accountname !=null and accountname != ''">
            accountname =  #{accountname},
        </if>
        <if test="accountno !=null and accountno != ''">
            accountno =  #{accountno},
        </if>
        <if test="executetype !=null and executetype != ''">
            executetype =  #{executetype},
        </if>
        <if test="executeamount !=null and executeamount != ''">
            executeamount = #{executeamount},
        </if>
        <if test="executedept !=null and executedept != ''">
            executedept =  #{executedept},
        </if>
        <if test="executedate !=null and executedate != ''">
            executedate =  #{executedate},
        </if>
        <if test="releaseserialno !=null and releaseserialno != ''">
            releaseserialno =  #{releaseserialno},
        </if>
        <if test="releasetime !=null and releasetime != ''">
            releasetime =  #{releasetime},
        </if>
        <if test="note !=null and note != ''">
            note =  #{note},
        </if>
        <if test="jgzhStatus !=null and jgzhStatus != ''">
            jgzh_status =  #{jgzhStatus},
        </if>
        <if test="jgzhDesc !=null and jgzhDesc != ''">
            jgzh_desc =  #{jgzhDesc},
        </if>
        update_by = #{updateBy},
        update_time = #{updateTime}
        where
        id = #{id}
    </update>
<!--    根据id修改信息-->
    <update id="updateJgzhByParam" >
        update xzp_jgzh_info
        set
        <if test="serialno !=null and serialno != ''">
            serialno  =  #{serialno},
        </if>
        <if test="bankid !=null and bankid != ''">
            bankid  =  #{bankid},
        </if>
        <if test="accountname !=null and accountname != ''">
            accountname =  #{accountname},
        </if>
        <if test="accountno !=null and accountno != ''">
            accountno =  #{accountno},
        </if>
        <if test="executetype !=null and executetype != ''">
            executetype =  #{executetype},
        </if>
        <if test="executeamount !=null and executeamount != ''">
            executeamount = #{executeamount},
        </if>
        <if test="executedept !=null and executedept != ''">
            executedept =  #{executedept},
        </if>
        <if test="executedate !=null and executedate != ''">
            executedate =  #{executedate},
        </if>
        <if test="releaseserialno !=null and releaseserialno != ''">
            releaseserialno =  #{releaseserialno},
        </if>
        <if test="releasetime !=null and releasetime != ''">
            releasetime =  #{releasetime},
        </if>
        <if test="note !=null and note != ''">
            note =  #{note},
        </if>
        <if test="jgzhStatus !=null and jgzhStatus != ''">
            jgzh_status =  #{jgzhStatus},
        </if>
        <if test="jgzhDesc !=null and jgzhDesc != ''">
            jgzh_desc =  #{jgzhDesc},
        </if>
            update_by = #{updateBy},
            update_time = #{updateTime}
        where
        id = #{id}
    </update>

    <!--批量修改数据-->
    <update id="updateJgzhBatchByParam" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
        update xzp_jgzh_info
        set
            <if test="item.accountname !=null and item.accountname != ''">
                accountname =  #{item.accountname},
            </if>
            <if test="item.accountno !=null and item.accountno != ''">
                accountno = #{item.accountno},
            </if>
            <if test="item.executetype !=null and item.executetype != ''">
                executetype = #{item.executetype},
            </if>
            <if test="item.executeamount !=null and item.executeamount != ''">
                executeamount = #{item.executeamount},
            </if>

            <if test="item.executedept !=null and item.executedept != ''">
                executedept = #{item.executedept},
            </if>
            <if test="item.executedate !=null and item.executedate != ''">
                executedate = #{item.executedate},
            </if>
            <if test="item.releaseserialno !=null and item.releaseserialno != ''">
                releaseserialno = #{item.releaseserialno},
            </if>
            <if test="item.releasetime !=null and item.releasetime != ''">
                releasetime = #{item.releasetime},
            </if>
            <if test="item.note !=null and item.note != ''">
                note = #{item.note},
            </if>
            <if test="item.bankid !=null and item.bankid != ''">
                bankid = #{item.bankid},
            </if>
                jgzh_status = '0',
                update_by = #{item.updateBy},
                update_time = #{item.updateTime}
        where
            serialno = #{item.serialno}
        </foreach>
    </update>

    <!--根据id批量修改数据-->
    <update id="updateJgzhBatchByIds" parameterType="java.util.List">
        <foreach collection="upStatusList" item="item" index="index" separator=";">
            update xzp_jgzh_info
            set
            <if test="item.jgzhStatus !=null and item.jgzhStatus != ''">
                jgzh_status =  #{item.jgzhStatus},
            </if>
            <if test="item.jgzhDesc !=null and item.jgzhDesc != ''">
                jgzh_desc =  #{item.jgzhDesc},
            </if>
            update_by = #{item.updateBy},
            update_time = #{item.updateTime}
            where jgzh_status not in ('1','3') and id in
            <foreach collection="item.jgzhList" item="flag" open="(" close=")" separator=","  >
                #{flag}
            </foreach>
        </foreach>
    </update>

    <insert id="insertJgzhData" parameterType="com.xm.xzp.model.entity.JgzhData">
        insert into xzp_jgzh_info (
            id,
            serialno,
            bankid,
            accountname,
            accountno,
            executetype,
            executeamount,
            executedept,
            executedate,
            releaseserialno,
            releasetime,
            note,
            create_time,
            create_by,
            data_source,
            jgzh_status,
            update_time,
            update_by
            )
        values (
            #{id},
            #{serialno},
            #{bankid},
            #{accountname},
            #{accountno},
            #{executetype},
            #{executeamount},
            #{executedept},
            #{executedate},
            #{releaseserialno},
            #{releasetime},
            #{note},
            #{createTime},
            #{createBy},
            #{dataSource},
            '0',
            #{createTime},
            #{createBy}
        )
    </insert>

</mapper>

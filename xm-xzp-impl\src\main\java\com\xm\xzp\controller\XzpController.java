package com.xm.xzp.controller;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.StaxDriver;
import com.xm.xzp.api.XzpApi;
import com.xm.xzp.aspect.PMCTLLog;
import com.xm.xzp.model.entity.JgzhData;
import com.xm.xzp.model.entity.XmlRsp;
import com.xm.xzp.model.entity.XzpData;
import com.xm.xzp.model.vo.XzpDataVo;
import com.xm.xzp.service.IJgzhDataService;
import com.xm.xzp.service.IXzpDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;
import org.json.XML;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 新中平
 * @date 2024/4/22 10:54
 */
@Slf4j
@RestController
@Component
public class XzpController implements XzpApi {

    @Resource
    private IXzpDataService xzpDataService;

    @Resource
    private IJgzhDataService jgzhDataService;

    /**
     * 查询
     * mortgageSn
     * mortgageConSn
     * mortgageName
     * */
    @Override
    @PMCTLLog(name = "外联接口获取按揭贷款信息1",action = "查询")
    public XzpDataVo queryByCondition(String mortgageSn, String mortgageConSn, String mortgageName) {
        log.info("[queryByCondition]请求参数：mortgageSn："+mortgageSn+",mortgageConSn:"+mortgageConSn+",mortgageName:"+mortgageName);
        LambdaQueryWrapper<XzpData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(XzpData::getLoanRctNo, mortgageSn).eq(XzpData::getContractNo, mortgageConSn);
        if (!StringUtils.isBlank(mortgageName)) {
            queryWrapper.eq(XzpData::getCustName, mortgageName);
        }
        XzpData xzpDataReq = new XzpData();
        xzpDataReq.setCustName(mortgageName);
        xzpDataReq.setLoanRctNo(mortgageSn);
        xzpDataReq.setContractNo(mortgageConSn);
        List<XzpData> list = xzpDataService.queryYxjfLsxdyeList(xzpDataReq);
        if (!list.isEmpty()) {
            //如果查询到数据 取时间最大的一个
            XzpData xzpData = list.stream().max(Comparator.comparing(XzpData::getSummDate)).get();
            List<XzpData> xzpDataList = list.stream().filter(item -> item.getSummDate().equals(xzpData.getSummDate())).collect(Collectors.toList());
            //根据文档要求 转换格式
            return XzpDataVo.success(xzpDataToMap(xzpDataList));
        }else{
            return XzpDataVo.error("请求失败");
        }

    }


    /**
     * -外部访问查询营销积分零售信贷业务数据
     *         获取按揭贷款信息
     *
     * @param jsonObject 请求的参数信息
     * */
    @PMCTLLog(name = "外联接口获取按揭贷款信息",action = "查询")
    public XzpDataVo queryYxjfLsxdye(net.sf.json.JSONObject jsonObject) {
        log.info("【queryYxjfLsxdye】请求参数json："+jsonObject.toString());
        HashMap map1 = JSON.parseObject(jsonObject.toString(),HashMap.class);
        Object usr = map1.get("usr");
        Object dataObject = map1.get("data");
        if(dataObject!=null){
            //带查询条件的参数
            String dataString = dataObject.toString();
            String mortgage_sn = null;
            String mortgage_con_sn = null;
            String mortgage_name = null;
            if(dataString.contains("[")){
                //data是json数组
                List paramList = com.alibaba.fastjson.JSONObject.parseArray(dataString);
                log.info("解析数组请求参数："+paramList.get(0).toString());
                HashMap map = JSON.parseObject(paramList.get(0).toString(),HashMap.class);
                log.info("请求参数map:{}", map.toString());
                if(map.get("mortgage_sn")!=null){
                    mortgage_sn = map.get("mortgage_sn").toString();
                    log.info("mortgage_sn:{}", mortgage_sn);
                }
                if(map.get("mortgage_con_sn")!=null){
                    mortgage_con_sn = map.get("mortgage_con_sn").toString();
                    log.info("mortgage_con_sn:{}", mortgage_con_sn);
                }
                if(map.get("mortgage_name")!=null){
                    mortgage_name = map.get("mortgage_name").toString();
                    log.info("mortgage_name:{}", mortgage_name);
                }
            }else{
                //data是json对象
                HashMap map = JSON.parseObject(dataString,HashMap.class);
                mortgage_sn = map.get("mortgage_sn").toString();
                mortgage_con_sn = map.get("mortgage_con_sn").toString();
                mortgage_name = map.get("mortgage_name").toString();
            }
            //LambdaQueryWrapper<XzpData> queryWrapper = new LambdaQueryWrapper<>();
            XzpData xzpDataReq = new XzpData();
            //抵押证明编号和抵押贷款合同号二选一必填
            log.info("判断抵押证明编号：{}--{},抵押贷款合同号：{}--{},是否空", mortgage_sn, StringUtils.isBlank(mortgage_sn), mortgage_con_sn, StringUtils.isBlank(mortgage_con_sn));
            if(StringUtils.isBlank(mortgage_sn)&&StringUtils.isBlank(mortgage_con_sn)){
                log.error("抵押证明编号:【{}】 和抵押贷款合同号:【{}】 都是空值！", mortgage_sn, mortgage_con_sn);
                return XzpDataVo.error("抵押证明编号和抵押贷款合同号至少需要填写一个！");
            }
            if(!StringUtils.isBlank(mortgage_sn)){
                xzpDataReq.setLoanRctNo(mortgage_sn);
                log.info("参数LoanRctNo:{}", mortgage_sn);
            }
            if(!StringUtils.isBlank(mortgage_con_sn)){
                xzpDataReq.setContractNo(mortgage_con_sn);
                log.info("参数ContractNo:{}", mortgage_con_sn);
            }
            if (!StringUtils.isBlank(mortgage_name)) {
                xzpDataReq.setCustName(mortgage_name);
                log.info("参数CustName:{}", mortgage_name);
            }
            List<XzpData> list = xzpDataService.queryYxjfLsxdyeList(xzpDataReq);
            log.info("获取到数据条数：{}", list.size());
            //List<XzpData> list = xzpDataService.list(queryWrapper);
            if (!list.isEmpty()) {
                //如果查询到数据 取时间最大的一个
                XzpData xzpData = list.stream().max(Comparator.comparing(XzpData::getSummDate)).get();
                List<XzpData> xzpDataList = list.stream().filter(item -> item.getSummDate().equals(xzpData.getSummDate())).collect(Collectors.toList());
                //根据文档要求 转换格式
                log.info("查询返回的结果"+xzpDataList.toString());
                return XzpDataVo.success(xzpDataToMap(xzpDataList));
            }else{
                return XzpDataVo.error("未查询到数据！请检查参数");
            }
        }else{
            return XzpDataVo.error("参数错误！"+jsonObject.toString());
        }
    }

    //暂时没有用
    @Override
    public XzpDataVo getYxjfLsxdye(String jsonString) {
        log.info("【getYxjfLsxdye】111请求参数json："+jsonString);
        HashMap map1 = JSON.parseObject(jsonString,HashMap.class);
        Object usr = map1.get("usr");
        Object dataObject = map1.get("data");
        if(dataObject!=null){
            //带查询条件的参数
            String dataString = dataObject.toString();
            String mortgage_sn = null;
            String mortgage_con_sn = null;
            String mortgage_name = null;
            if(dataString.contains("[")){
                //data是json数组
                List paramList = com.alibaba.fastjson.JSONObject.parseArray(dataString);
                log.info("解析数组请求参数："+paramList.get(0).toString());
                HashMap map = JSON.parseObject(paramList.get(0).toString(),HashMap.class);
                if(map.get("mortgage_sn")!=null){
                    mortgage_sn = map.get("mortgage_sn").toString();
                }
                if(map.get("mortgage_con_sn")!=null){
                    mortgage_con_sn = map.get("mortgage_con_sn").toString();
                }
                if(map.get("mortgage_name")!=null){
                    mortgage_name = map.get("mortgage_name").toString();
                }

            }else{
                //data是json对象
                HashMap map = JSON.parseObject(dataString,HashMap.class);
                mortgage_sn = map.get("mortgage_sn").toString();
                mortgage_con_sn = map.get("mortgage_con_sn").toString();
                mortgage_name = map.get("mortgage_name").toString();
            }


            LambdaQueryWrapper<XzpData> queryWrapper = new LambdaQueryWrapper<>();
            //抵押证明编号和抵押贷款合同号二选一必填
            if(StringUtils.isBlank(mortgage_sn)&&StringUtils.isBlank(mortgage_con_sn)){
                return XzpDataVo.error("抵押证明编号和抵押贷款合同号至少需要填写一个！");
            }
            if(!StringUtils.isBlank(mortgage_sn)){
                queryWrapper.eq(XzpData::getLoanRctNo, mortgage_sn);
            }
            if(!StringUtils.isBlank(mortgage_con_sn)){
                queryWrapper.eq(XzpData::getContractNo, mortgage_con_sn);
            }
            if (!StringUtils.isBlank(mortgage_name)) {
                queryWrapper.eq(XzpData::getCustName, mortgage_name);
            }
            List<XzpData> list = xzpDataService.list(queryWrapper);
            if (!list.isEmpty()) {
                //如果查询到数据 取时间最大的一个
                XzpData xzpData = list.stream().max(Comparator.comparing(XzpData::getSummDate)).get();
                List<XzpData> xzpDataList = list.stream().filter(item -> item.getSummDate().equals(xzpData.getSummDate())).collect(Collectors.toList());
                //根据文档要求 转换格式
                return XzpDataVo.success(xzpDataToMap(xzpDataList));
            }else{
                return XzpDataVo.error("未查询到数据！请检查参数");
            }
        }else{
            return XzpDataVo.error("参数错误！"+jsonString);
        }
    }

    /**
     * 查询监管账户被执行信息变动情况反馈
     * 请求 xml
     * 响应 xml
     * */
    @Override
    @PMCTLLog(name = "外联接口查询监管账户被执行信息变动情况反馈",action = "查询")
    public String getJgzhInfo(HttpServletRequest request, ServletResponse response) {
        // 数据提取（head）
        StringBuffer reqXmlHeadData = new StringBuffer();
        // 数据提取（body）
        StringBuffer reqXmlData = new StringBuffer();
        XStream xstream = new XStream(new StaxDriver());//不需要XPP3库开始使用Java6

        try {
            InputStream inputStream = request.getInputStream();
            String s;
            BufferedReader in = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            while ((s = in.readLine()) != null) {
                reqXmlData.append(s);
            }
            in.close();
            inputStream.close();
        } catch (IOException e) {
            System.out.println("流解析xml数据异常!");
            e.printStackTrace();
        }
        //判断请求数据是否为空
        if (reqXmlData.length() <= 0) {
            System.out.println("请求数据为空!");
            return xstream.toXML("请求数据为空!请输入条件");
        }
        //将xml转化为json类型数据读取参数
        JSONObject jsonObject = XML.toJSONObject(reqXmlData.toString());
        log.info("xml 转换为json:{}", jsonObject.toString());
        JSONObject headJson = jsonObject.getJSONObject("content").getJSONObject("head");
        String usr = headJson.get("usr").toString();
        String serviceno = headJson.get("serviceno").toString();
        String optname =headJson.get("optname").toString();
        String pwd = headJson.get("pwd").toString();
        String signmsg = headJson.get("signmsg").toString();

        JSONObject bodyJson = jsonObject.getJSONObject("content").getJSONObject("body");
        //查询条件
        String note = bodyJson.get("note").toString();
        String executetype = bodyJson.get("executetype").toString();
        String executedate = bodyJson.get("executedate").toString();
        String releaseserialno = bodyJson.get("releaseserialno").toString();
        String serialno = bodyJson.get("serialno").toString();
        String releasetime = bodyJson.get("releasetime").toString();
        String accountname = bodyJson.get("accountname").toString();
        String accountno = bodyJson.get("accountno").toString();
        String executeamount = bodyJson.get("executeamount").toString();
        String executedept = bodyJson.get("executedept").toString();


        String statecode = "0";
        String rspMsg="待审核";
        //开始查询表数据，查看反馈情况
        JgzhData queryData = new JgzhData();
        queryData.setSerialno("***************");//执行流水号，唯一值
        JgzhData jgzhData = jgzhDataService.queryJgzhByParam(queryData);
        String jgzhStatus = "0";//待审核
        if(jgzhData!=null){
            jgzhStatus = jgzhData.getJgzhStatus();
        }
        if("0".equals(jgzhStatus)){
            rspMsg = "待审核";
        }else  if("1".equals(jgzhStatus)){
            rspMsg = "审核通过，待反馈";
        }else if("2".equals(jgzhStatus)){
            rspMsg = "审核不通过，请重新提交";
        }else if("3".equals(jgzhStatus)){
            rspMsg = "反馈成功";
        }else if("4".equals(jgzhStatus)){
            rspMsg = "反馈失败";
        }

        //if
        // 返回数据
        XmlRsp responseXmlData = new XmlRsp();
        responseXmlData.setStatecode(statecode);
        responseXmlData.setMsg(rspMsg);
        responseXmlData.setSerialno(serviceno);
        responseXmlData.setIssuccess(statecode);

        xstream.alias("content", XmlRsp.class);//为类名节点重命名
        //XML序列化
        String xml = xstream.toXML(responseXmlData);
        log.info("return xml : {}",xml);
        return xstream.toXML(responseXmlData);  //XStream的方法，直接将对象转换成 xml数据
    }

    /**
     * 将数据转换成 特定格式
     *
     * @return map
     */
    public List<Map<String, String>> xzpDataToMap(List<XzpData> xzpDataList) {
        List<Map<String, String>> list = new ArrayList<>();
        xzpDataList.forEach(item -> {
            Map<String, String> map = new HashMap<>();
            BigDecimal LOAN_OUTACC_AMT = item.getLoanOutaccAmt();//贷款出账金额
            BigDecimal LOAN_CONTRACT_AMT = item.getLoanContractAmt();//贷款合同金额
            int chuzhang = LOAN_OUTACC_AMT.compareTo(BigDecimal.ZERO);
            String lendingStaus = "0";
            if(chuzhang == 0){//出账金额为0，未放贷
                lendingStaus = "0";
            }else{
                int num = LOAN_CONTRACT_AMT.compareTo(LOAN_OUTACC_AMT);
                if(num == 0){
                    lendingStaus = "2";
                }else if(num > 0){//合同金额大于出账金额，则部分放贷
                    lendingStaus = "1";
                }
            }
            //0-未放贷；1-部分放贷；2-全部放贷;
            map.put("lending_status",lendingStaus);
            map.put("lending_date",item.getLoanRctBgnDate());
            map.put("lending_bank_name",item.getBusiBelongInstName());
            map.put("lending_account",item.getDisburseAccno());
            map.put("lending_account_name",item.getCustName());
            map.put("lending_amount",item.getLoanOutaccAmt().toString());
            map.put("lending_bak","");
            list.add(map);
        });
        return list;
    }


}


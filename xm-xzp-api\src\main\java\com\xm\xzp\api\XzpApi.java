package com.xm.xzp.api;


import com.xm.xzp.model.vo.XzpDataVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
//import org.json.JSONObject;
import net.sf.json.JSONObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 */
@ResponseBody
@RequestMapping("/api/admin/xzp")
@Api(tags = "新中平管理")
@Validated
public interface XzpApi {

    /**
     * 根据条件查询数据
     *
     * @param mortgageSn    抵押证明编号
     * @param mortgageConSn 抵押贷款合同号
     * @param mortgageName  抵押人名字
     * @return 返回固定 格式
     */
    @ApiOperation(value = "根据条件查询数据", notes = "queryByCondition")
    @PostMapping("/queryByCondition")
    XzpDataVo queryByCondition(@RequestParam(value = "mortgage_sn") String mortgageSn, @RequestParam(value = "mortgage_con_sn") String mortgageConSn, @RequestParam(value = "mortgage_name", required = false) String mortgageName);

    @ApiOperation(value = "查询营销积分零售信贷业务数据", notes = "queryYxjfLsxdye")
    @PostMapping("/queryYxjfLsxdye")
    XzpDataVo queryYxjfLsxdye(@RequestBody JSONObject jsonObject);

    @ApiOperation(value = "查询营销积分零售信贷业务数据", notes = "getYxjfLsxdye")
    @PostMapping("/getYxjfLsxdye")
    XzpDataVo getYxjfLsxdye(String jsonString);

    @ApiOperation(value = "查询监管账户信息变动情况反馈", notes = "getJgzhInfo")
    @PostMapping("/getJgzhInfo")
    String getJgzhInfo(HttpServletRequest request, ServletResponse response);

}

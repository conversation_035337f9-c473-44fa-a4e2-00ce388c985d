package com.xm.xzp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.mapper.TbZjjgAcctInfoMapper;
import com.xm.xzp.model.entity.TbZjjgAcctInfo;
import com.xm.xzp.model.vo.AcctInfoQueryVo;
import com.xm.xzp.service.ITbZjjgAcctInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 资金监管账户信息服务实现类
 * <AUTHOR>
 */
@Service
@Slf4j
public class TbZjjgAcctInfoServiceImpl extends ServiceImpl<TbZjjgAcctInfoMapper, TbZjjgAcctInfo> implements ITbZjjgAcctInfoService {

    @Resource
    private TbZjjgAcctInfoMapper tbZjjgAcctInfoMapper;

    @Override
    public List<TbZjjgAcctInfo> suggestAcctInfo(String keyword) {
        try {
            String trimmedKeyword = StringUtils.hasText(keyword) ? keyword.trim() : null;
            List<TbZjjgAcctInfo> result = tbZjjgAcctInfoMapper.selectAcctInfoByKeyword(trimmedKeyword);
            if (StringUtils.hasText(keyword)) {
                log.info("根据关键字[{}]查询到{}条账户信息", keyword, result.size());
            } else {
                log.info("查询全量账户信息，共{}条", result.size());
            }
            return result;
        } catch (Exception e) {
            log.error("查询账户信息失败，关键字：{}", keyword, e);
            return Collections.emptyList();
        }
    }

    @Override
    public PageInfo<TbZjjgAcctInfo> queryAcctInfoPage(AcctInfoQueryVo queryVo, Integer pageNum, Integer pageSize) {
        try {
            PageHelper.startPage(pageNum, pageSize);
            List<TbZjjgAcctInfo> list = tbZjjgAcctInfoMapper.selectAcctInfoByCondition(queryVo);
            log.info("分页查询账户信息，查询条件：{}，查询到{}条记录", queryVo, list.size());
            return new PageInfo<>(list);
        } catch (Exception e) {
            log.error("分页查询账户信息失败，查询条件：{}", queryVo, e);
            return new PageInfo<>(Collections.emptyList());
        }
    }

    @Override
    public boolean addAcctInfo(TbZjjgAcctInfo acctInfo) {
        try {
            // 检查是否存在重复的账户信息（根据公司账户和户名判断）
            if (StringUtils.hasText(acctInfo.getCpabAccId()) && StringUtils.hasText(acctInfo.getAcctNm())) {
                long count = this.lambdaQuery()
                        .eq(TbZjjgAcctInfo::getCpabAccId, acctInfo.getCpabAccId())
                        .eq(TbZjjgAcctInfo::getAcctNm, acctInfo.getAcctNm())
                        .count();
                if (count > 0) {
                    log.warn("账户信息已存在，公司账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
                    return false;
                }
            }

            // 设置业务类型为资金监管
            if (!StringUtils.hasText(acctInfo.getTransType())) {
                acctInfo.setTransType("zzg");
            }

            // 设置交易日期（如果未设置）
            if (!StringUtils.hasText(acctInfo.getTranDt())) {
                acctInfo.setTranDt(java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd")));
            }

            // 设置交易时间戳（如果未设置）
            if (!StringUtils.hasText(acctInfo.getTranTime())) {
                acctInfo.setTranTime(java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            }

            boolean result = this.save(acctInfo);
            if (result) {
                log.info("新增账户信息成功，账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            } else {
                log.warn("新增账户信息失败，账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            }
            return result;
        } catch (Exception e) {
            log.error("新增账户信息异常，账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm(), e);
            return false;
        }
    }

    @Override
    public boolean updateAcctInfo(TbZjjgAcctInfo acctInfo) {
        try {
            // 检查必填字段
            if (!StringUtils.hasText(acctInfo.getCpabAccId()) || !StringUtils.hasText(acctInfo.getAcctNm())) {
                log.error("修改账户信息失败，公司账户和户名不能为空");
                return false;
            }

            // 检查记录是否存在
            TbZjjgAcctInfo existingInfo = this.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getCpabAccId, acctInfo.getCpabAccId())
                    .eq(TbZjjgAcctInfo::getAcctNm, acctInfo.getAcctNm())
                    .one();

            if (existingInfo == null) {
                log.warn("修改账户信息失败，记录不存在，公司账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
                return false;
            }

            // 更新交易时间戳
            if (!StringUtils.hasText(acctInfo.getTranTime())) {
                acctInfo.setTranTime(java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            }

            // 执行更新
            boolean result = this.lambdaUpdate()
                    .eq(TbZjjgAcctInfo::getCpabAccId, acctInfo.getCpabAccId())
                    .eq(TbZjjgAcctInfo::getAcctNm, acctInfo.getAcctNm())
                    .update(acctInfo);

            if (result) {
                log.info("修改账户信息成功，公司账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            } else {
                log.warn("修改账户信息失败，公司账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm());
            }
            return result;
        } catch (Exception e) {
            log.error("修改账户信息异常，公司账户：{}，户名：{}", acctInfo.getCpabAccId(), acctInfo.getAcctNm(), e);
            return false;
        }
    }

    @Override
    public boolean deleteAcctInfo(String cpabAccId, String acctNm) {
        try {
            // 检查必填字段
            if (!StringUtils.hasText(cpabAccId) || !StringUtils.hasText(acctNm)) {
                log.error("删除账户信息失败，公司账户和户名不能为空");
                return false;
            }

            // 检查记录是否存在
            long count = this.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getCpabAccId, cpabAccId)
                    .eq(TbZjjgAcctInfo::getAcctNm, acctNm)
                    .count();

            if (count == 0) {
                log.warn("删除账户信息失败，记录不存在，公司账户：{}，户名：{}", cpabAccId, acctNm);
                return false;
            }

            // 执行删除
            boolean result = this.lambdaUpdate()
                    .eq(TbZjjgAcctInfo::getCpabAccId, cpabAccId)
                    .eq(TbZjjgAcctInfo::getAcctNm, acctNm)
                    .remove();

            if (result) {
                log.info("删除账户信息成功，公司账户：{}，户名：{}", cpabAccId, acctNm);
            } else {
                log.warn("删除账户信息失败，公司账户：{}，户名：{}", cpabAccId, acctNm);
            }
            return result;
        } catch (Exception e) {
            log.error("删除账户信息异常，公司账户：{}，户名：{}", cpabAccId, acctNm, e);
            return false;
        }
    }

    @Override
    public TbZjjgAcctInfo getAcctInfo(String cpabAccId, String acctNm) {
        try {
            // 检查必填字段
            if (!StringUtils.hasText(cpabAccId) || !StringUtils.hasText(acctNm)) {
                log.error("查询账户信息失败，公司账户和户名不能为空");
                return null;
            }

            TbZjjgAcctInfo result = this.lambdaQuery()
                    .eq(TbZjjgAcctInfo::getCpabAccId, cpabAccId)
                    .eq(TbZjjgAcctInfo::getAcctNm, acctNm)
                    .one();

            if (result != null) {
                log.debug("查询账户信息成功，公司账户：{}，户名：{}", cpabAccId, acctNm);
            } else {
                log.debug("查询账户信息为空，公司账户：{}，户名：{}", cpabAccId, acctNm);
            }
            return result;
        } catch (Exception e) {
            log.error("查询账户信息异常，公司账户：{}，户名：{}", cpabAccId, acctNm, e);
            return null;
        }
    }
}
package com.xm.xzp.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xm.xzp.mapper.TbZjjgAcctInfoMapper;
import com.xm.xzp.model.entity.TbZjjgAcctInfo;
import com.xm.xzp.service.ITbZjjgAcctInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 资金监管账户信息服务实现类
 * <AUTHOR>
 */
@Service
@Slf4j
public class TbZjjgAcctInfoServiceImpl extends ServiceImpl<TbZjjgAcctInfoMapper, TbZjjgAcctInfo> implements ITbZjjgAcctInfoService {

    @Resource
    private TbZjjgAcctInfoMapper tbZjjgAcctInfoMapper;

    @Override
    public List<TbZjjgAcctInfo> suggestAcctInfo(String keyword) {
        try {
            String trimmedKeyword = StringUtils.hasText(keyword) ? keyword.trim() : null;
            List<TbZjjgAcctInfo> result = tbZjjgAcctInfoMapper.selectAcctInfoByKeyword(trimmedKeyword);
            if (StringUtils.hasText(keyword)) {
                log.info("根据关键字[{}]查询到{}条账户信息", keyword, result.size());
            } else {
                log.info("查询全量账户信息，共{}条", result.size());
            }
            return result;
        } catch (Exception e) {
            log.error("查询账户信息失败，关键字：{}", keyword, e);
            return Collections.emptyList();
        }
    }
}
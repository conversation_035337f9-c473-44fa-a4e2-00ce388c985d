<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xm.xzp.mapper.XzpDataMapper">
    <!-- 查询按揭贷款信息- 新一代数据库 -->
    <select id="queryYxjfLsxdyeList" parameterType="java.lang.String" resultType="com.xm.xzp.model.entity.XzpData">
         select
            T1.SUMM_DATE,
            T1.LOAN_RCT_NO,
            T2.LOAN_RCT_ID,
            T1.LOAN_STATUS_CODE,
            T1.LOAN_RCT_BGN_DATE,
            T2.PAYEE_OAC_BANK_NAME BUSI_BELONG_INST_NAME,
            T2.PAYEE_ACC_NO DISBURSE_ACCNO,
            T2.PAYEE_NAME CUST_NAME,
            T1.LOAN_OUTACC_AMT,
            T1.LOAN_CONTRACT_AMT
        from  T_JS_YXJF_LSXDYE  T1
        left join T_JS_LSXD_STZF_JGB T2
          on  T1.LOAN_APPLY_ID = T2.LOAN_APPLY_ID and T1.SUMM_DATE = T2.SUMM_DATE
        where T2.PAY_STATUS = '2'
          and T1.SUMM_DATE = (select MAX(summ_date) from T_JS_YXJF_LSXDYE)
        <if test="contractNo !=null and contractNo != ''">
            and  T1.contract_no  =  #{contractNo}
        </if>
        <if test="loanRctNo !=null and loanRctNo != ''">
            and  T1.loan_rct_no  =  #{loanRctNo}
        </if>
        <if test="custName !=null and custName != ''">
            and  T1.cust_name  like '%'||#{custName}||'%'
        </if>
    </select>
</mapper>

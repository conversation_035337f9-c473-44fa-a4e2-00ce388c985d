package com.xm.xzp.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xm.xzp.model.entity.TbZjjgAcctInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资金监管账户信息 Mapper 接口
 * <AUTHOR>
 */
@Mapper
@DS("datasource2")
public interface TbZjjgAcctInfoMapper extends BaseMapper<TbZjjgAcctInfo> {

    /**
     * 根据关键字模糊查询账户信息（用于联想下拉控件）
     * @param keyword 关键字，匹配户名或公司账户
     * @return 账户信息列表
     */
    List<TbZjjgAcctInfo> selectAcctInfoByKeyword(@Param("keyword") String keyword);
}
package com.xm.xzp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "商户操作码分组查询")
public class MerchOpeGroupVo {
    @ApiModelProperty("商户号")
    private String merchId;

    @ApiModelProperty("操作码")
    private String opeCd;

    @ApiModelProperty("产品名称")
    private String prdtNm;

    @ApiModelProperty("商户操作开关标志")
    private String merchOpeOnOffFg;

    @ApiModelProperty("商户操作类型")
    private String merchOpeType;

    @ApiModelProperty("开通日期起")
    private String openDtStart;

    @ApiModelProperty("开通日期止")
    private String openDtEnd;

    @ApiModelProperty("总数")
    private Integer count;
}
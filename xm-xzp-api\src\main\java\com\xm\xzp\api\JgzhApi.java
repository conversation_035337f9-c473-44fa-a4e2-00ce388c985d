package com.xm.xzp.api;


import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.response.RestResponse;
import com.xm.xzp.model.entity.JgzhData;
import com.xm.xzp.model.entity.JgzhSpData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * <AUTHOR>
 */
@ResponseBody
@RequestMapping("/api/admin/xzp")
@Api(tags = "新中平管理")
@Validated
public interface JgzhApi {
    /**
     * 监管账户信息变动情况列表查询
     *
     * @param pageNum    当前页
     * @param jgzhData 接收用户数据
     * @param pageSize   每页沙宣数量
     * @return 数据列表
     */
    @ApiOperation(value = "监管账户信息变动情况查询列表", notes = "jgzhList")
    @PostMapping("/jgzhList")
    RestResponse<PageInfo<JgzhData>> queryJgzhList(@RequestBody JgzhData jgzhData, @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum, @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize);

    @ApiOperation(value = "监管账户被执行信息详情", notes = "JgzhDetail")
    @GetMapping("/JgzhDetail/{id}")
    RestResponse<JgzhData> viewJgzh(@PathVariable("id") String id);

    @ApiOperation(value = "新增监管账户被执行信息", notes = "addJgzhInfo")
    @PostMapping(value = "/addJgzhInfo")
    RestResponse<String> addJgzhInfo(@RequestBody JgzhData jgzhData);

    @ApiOperation(value = "删除一个", notes = "removeJgzh")
    @PostMapping("/removeJgzh/{id}")
    RestResponse<String> removeJgzh(@PathVariable("id") String id);

    @ApiOperation(value = "批量删除", notes = "removeBatchJgzh")
    @PostMapping("/removeBatchJgzh")
    RestResponse<Boolean> removeBatchJgzh(@RequestBody List<String> ids);

    @ApiOperation(value = "更新监管账户被执行信息状态", notes = "editJgzh")
    @PostMapping("/editJgzh")
    RestResponse<JgzhData> editJgzh(@RequestBody  JgzhData jgzhData);

    @ApiOperation(value = "批量更新监管账户被执行信息状态", notes = "editJgzhBatch")
    @PostMapping("/editJgzhBatch")
    RestResponse<JgzhData> batchUpdateInfo(@RequestBody  JgzhData jgzhData);


    /**
     * 文件数据导入
     *
     * @param file 文件
     * @return 返回
     * @throws Exception 异常抛出
     */
    @ApiOperation(value = "导入监管账户被执行信息信息", notes = "importJgzh")
    @PostMapping(value = "/actions/import", consumes = "multipart/form-data")
    RestResponse<String> importJgzh(@RequestPart("file") MultipartFile file) throws Exception;

    /**
     * 文件数据增量导入
     *
     * @param file 文件
     * @return 返回
     * @throws Exception 异常抛出
     */
    @ApiOperation(value = "增量导入监管账户被执行信息信息", notes = "importJgzhAdd")
    @PostMapping(value = "/actions/import_add", consumes = "multipart/form-data")
    RestResponse<String> importJgzhAdd(@RequestPart("file") MultipartFile file) throws Exception;

    /**
     * 查询敏感信息
     *
     */
    @ApiOperation(value = "查询敏感信息", notes = "getJgzhSensitiveInfo")
    @GetMapping("/jgzh/sensitive/get/{id}/{sensitiveStatus}")
    RestResponse<JgzhData> getJgzhSensitiveInfo(@PathVariable("id") String id,
                                                  @PathVariable("sensitiveStatus") String sensitiveStatus);


}

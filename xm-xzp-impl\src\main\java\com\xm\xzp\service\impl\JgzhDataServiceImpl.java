package com.xm.xzp.service.impl;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.psbc.pfpj.yoaf.context.util.UserInfoContext;
import com.psbc.pfpj.yoaf.response.exception.BizException;
import com.psbc.pfpj.yoaf.response.util.ResponseUtils;
import com.xm.xzp.enums.SensitiveStatusEnum;
import com.xm.xzp.mapper.JgzhDataMapper;
import com.xm.xzp.mapper.JgzhSpDataMapper;
import com.xm.xzp.model.entity.JgzhData;
import com.xm.xzp.model.entity.XzpOpLog;
import com.xm.xzp.model.vo.ExcelJgzhVO;
import com.xm.xzp.service.IJgzhDataService;
import com.xm.xzp.service.IXzpOpLogService;
import com.xm.xzp.util.AdminConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class JgzhDataServiceImpl extends ServiceImpl<JgzhDataMapper, JgzhData> implements IJgzhDataService {

    @Resource
    private JgzhDataMapper jgzhDataMapper;
    @Resource
    private IXzpOpLogService xzpOpLogService;
    @Resource
    private JgzhSpDataMapper jgzhSpDataMapper;


    /**
     * 分页查询监管账户被执行变动情况信息
     * */
    @Override
    public PageInfo<JgzhData> queryPage(JgzhData jgzhData, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<JgzhData> list = this.list(getJgzhQueryWrapper(jgzhData));
        if (!list.isEmpty()) {
            System.out.println("查询监管账户条数-->"+list.size());
        }
        // 默认隐藏信息
        for (JgzhData em : list) {
            String accountno = em.getAccountno();
            String accountname = em.getAccountname();
            if (StringUtils.isNotEmpty(accountno)) {
                accountno = accountno.replaceAll(AdminConstants.ID_CARD_DES_REGEX, AdminConstants.ID_CARD_DES_SIGN);
                em.setAccountno(accountno);
            }

            if (StringUtils.isNotEmpty(accountname)) {
                accountname = accountname.replaceAll("(?<=.{1}).", "*");
                em.setAccountname(accountname);
            }
        }
        return new PageInfo<>(list);
    }

    /**
     * 新增监管账户被执行变动情况信息
     * */
    @Override
    public boolean addJgzh(JgzhData jgzhData) {
      //  log.info("新增监管账户被执行变动情况信息:[{}]", jgzhData);
        log.info("新增监管账户被执行变动情况信息:[{}]", "");
        //记录日志
        XzpOpLog xzpOpLog = new XzpOpLog();
        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值

        try{
            try{
                userName = UserInfoContext.currentUserName();
            }catch(Exception e){
                log.error("[addJgzh]获取用户名异常，"+e);
            }
            jgzhData.setCreateTime(dt);//创建时间
            jgzhData.setCreateBy(userName);//创建人
            boolean a = jgzhDataMapper.insertJgzhData(jgzhData);
            if(a){
                log.info("新增监管账户被执行变动情况信息入库成功");
                jgzhSpDataMapper.insertJgzhSpData(jgzhData);
                xzpOpLog.setSpeReleId(jgzhData.getId());
                xzpOpLog.setDealRes("成功");
                xzpOpLog.setOpinion("提交反馈成功,待审核");
            }else{
                log.error("新增监管账户被执行变动情况信息入库失败");
                xzpOpLog.setDealRes("失败");
                xzpOpLog.setOpinion("提交反馈失败,请联系管理员！");
            }
        }catch (Exception e){
            log.error("新增监管账户项目异常：{}", e.getMessage());
            xzpOpLog.setDealRes("失败");
            xzpOpLog.setOpinion("提交反馈失败,请联系管理员！");
            return false;
        }finally {
            xzpOpLog.setDealUser(userName);
            xzpOpLog.setCreateBy(userName);
            xzpOpLog.setStartTime(dt);
            xzpOpLog.setCreateTime(dt);
            xzpOpLog.setStepId("申请");
            xzpOpLog.setStepName("新增监管账户被执行反馈申请信息");
            xzpOpLog.setApplyType("jgzh");
            xzpOpLog.setFlowId("xzp_jgzh_info");
            xzpOpLogService.addLog(xzpOpLog);
        }
        return true;
    }
    
    /**
     * 下载监管账户被执行信息导入模板
     * */
    @Override
    public void downJgzhTemplate(HttpServletResponse response) throws IOException {
        String filePath = "/doc/xzp/监管账户被执行信息导入模板.xls";
        String fileName = URLEncoder.encode("监管账户被执行信息导入模板.xls", "UTF-8");
        //记录日志
        XzpOpLog xzpOpLog = new XzpOpLog();
        xzpOpLog.setStepId("下载");
        xzpOpLog.setStepName("下载监管账户被执行反馈信息导入模板");
        xzpOpLog.setDealRes("成功");
        xzpOpLog.setOpinion("下载监管账户被执行反馈信息导入模板成功");

        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值
        try{
            userName = UserInfoContext.currentUserName();
        }catch(Exception e){
            log.error("[downJgzhTemplate]获取用户名异常，"+e);
        }
        xzpOpLog.setDealUser(userName);
        xzpOpLog.setCreateBy(userName);
        xzpOpLog.setStartTime(dt);
        xzpOpLog.setCreateTime(dt);
        xzpOpLog.setApplyType("jgzh");
        xzpOpLog.setFlowId("xzp_jgzh_info");
        xzpOpLogService.addLog(xzpOpLog);
        // 下载模板
        ResponseUtils.downloadFile(response, fileName, filePath);
    }

    /**
     * 导出数据
     * */
    @Override
    public Workbook exportJgzh(JgzhData jgzhData) {
        TemplateExportParams params = new TemplateExportParams("doc/xzp/监管账户被执行信息导出模板.xls");
        log.info("获取导出数据>>>>>>>>");
//        List<ExcelJgzhVo> jgzhs =  jgzhDataMapper.selectExportJgzh(jgzhData);
        List<JgzhData> jgzhs =  this.list(getJgzhQueryWrapper(jgzhData));
        Map<String, Object> map = new HashMap<>(jgzhs.size());
//        List<DictEntry> jgzhQueryStatus = dictQueryUtils.getDictItemList(Collections.singletonList("jgzh_query_status"));
        //处理字典表数据
//        if (!jgzhList.isEmpty() && !jgzhQueryStatus.isEmpty()) {
//            jgzhList.forEach(jgzh -> {
//                DictEntry dictEntry = jgzhQueryStatus.stream().filter(dict -> dict.getDictId().equals(jgzh.getStatus())).findAny().orElse(null);
//                if (dictEntry != null) {
//                    jgzh.setStatus(dictEntry.getDictName());
//                }
//            });
//        }
        map.put("jgzhs", jgzhs);
        //记录日志
        XzpOpLog xzpOpLog = new XzpOpLog();
        xzpOpLog.setStepId("导出");
        xzpOpLog.setStepName("导出监管账户被执行反馈信息");
        xzpOpLog.setDealRes("成功");
        xzpOpLog.setOpinion("导出监管账户被执行反馈信息成功");

        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值
        try{
            userName = UserInfoContext.currentUserName();
        }catch(Exception e){
            log.error("[exportJgzh]获取用户名异常，"+e);
        }
        xzpOpLog.setDealUser(userName);
        xzpOpLog.setCreateBy(userName);
        xzpOpLog.setStartTime(dt);
        xzpOpLog.setCreateTime(dt);
        xzpOpLog.setApplyType("jgzh");
        xzpOpLog.setFlowId("xzp_jgzh_info");
        xzpOpLogService.addLog(xzpOpLog);
//        log.info("导出监管账户被执行信息信息列表：---->"+map.toString());
        return ExcelExportUtil.exportExcel(params, map);
    }

    @Override
    public JgzhData queryJgzhByParam(JgzhData queryData) {
         log.info("查询监管账户被执行反馈信息:");
        //log.info("查询监管账户被执行反馈信息:[queryData:{}]", queryData.toString());
        JgzhData jgzhData = jgzhDataMapper.selectInfoByParam(queryData);
        //记录日志
        XzpOpLog xzpOpLog = new XzpOpLog();
        xzpOpLog.setStepId("查询");
        xzpOpLog.setStepName("查询监管账户被执行反馈信息");
        xzpOpLog.setDealRes("成功");
        xzpOpLog.setOpinion("查询监管账户被执行反馈信息成功："+jgzhData.getSerialno());
        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值
        try{
            userName = UserInfoContext.currentUserName();
        }catch(Exception e){
            log.error("[queryJgzhByParam]获取用户名异常，"+e);
        }
        xzpOpLog.setDealUser(userName);
        xzpOpLog.setCreateBy(userName);
        xzpOpLog.setStartTime(dt);
        xzpOpLog.setCreateTime(dt);
        xzpOpLog.setApplyType("jgzh");
        xzpOpLog.setFlowId("xzp_jgzh_info");
        xzpOpLogService.addLog(xzpOpLog);
        return jgzhData;
    }

    @Override
    public JgzhData getJgzhSensitiveInfo(String id, String sensitiveStatus) {
        log.info("显示隐藏监管账户敏感信息:[id:{}, sensitiveStatus:{}]", id, sensitiveStatus);

        // 查询人员信息
        LambdaQueryWrapper<JgzhData> queryWrapper = new LambdaQueryWrapper<>();
        // 设置查询字段
        queryWrapper.select(JgzhData::getAccountno, JgzhData::getAccountname);
        queryWrapper.eq(JgzhData::getId, id);
        JgzhData em = jgzhDataMapper.selectOne(queryWrapper);

        // 组装用户返回信息
        JgzhData sensitiveInfoVO = new JgzhData();
        BeanUtils.copyProperties(em, sensitiveInfoVO);
        // 设置脱敏
        if (SensitiveStatusEnum.UN_SENSITIVE_STATUS.statusCode().equals(sensitiveStatus)) {
            sensitiveInfoVO.setSensitiveStatus(SensitiveStatusEnum.SENSITIVE_STATUS.statusCode());

            String accountno = sensitiveInfoVO.getAccountno();
            if (StringUtils.isNotEmpty(accountno)) {
                accountno = accountno.replaceAll(AdminConstants.ID_CARD_DES_REGEX, AdminConstants.ID_CARD_DES_SIGN);
                sensitiveInfoVO.setAccountno(accountno);
            }

            String accountname = sensitiveInfoVO.getAccountname();
            if (StringUtils.isNotEmpty(accountname)) {
                accountname = accountname.replaceAll("(?<=.{1}).", "*");
                sensitiveInfoVO.setAccountname(accountname);
            }
        } else {
            // 设置未脱敏
            sensitiveInfoVO.setSensitiveStatus(SensitiveStatusEnum.UN_SENSITIVE_STATUS.statusCode());
        }

        return sensitiveInfoVO;
    }


    /**
     * 全量导入监管账户被执行信息数据
     * */

    @Override
    public void importJgzh(MultipartFile file) throws Exception {
        //记录日志
        XzpOpLog xzpOpLog = new XzpOpLog();
        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值
        try{
            userName = UserInfoContext.currentUserName();
        }catch(Exception e){
            log.error("[importJgzh]获取用户名异常，"+e);
        }
       // log.info("当前登录人>>>>"+userName);

        ImportParams params = new ImportParams();
        // 设置表格标题行数
        params.setTitleRows(2);//前2行，说明
        // 设置标题行数
        params.setHeadRows(1);//标题
        // 开启excel校验 注解校验
        params.setNeedVerify(true);
        // 导入模板信息
       // log.info("params>>>>"+params.toString());
        //ExcelImportResult<ExcelJgzhVo> result = ExcelImportUtil.importExcelMore(file.getInputStream(), ExcelJgzhVo.class, params);
        long t1= System.currentTimeMillis();
        List<ExcelJgzhVO> successList = ExcelImportUtil.importExcel(file.getInputStream(), ExcelJgzhVO.class, params);
        long t2= System.currentTimeMillis();
        log.info("解析excel，获取到数据条数>>>>"+successList.size());
        log.info("解析excel，耗时约>>>>"+(t2-t1)+" 秒");
        List<JgzhData> jgzhDataList = new ArrayList<>();
        if (!successList.isEmpty()) {
            for (ExcelJgzhVO jgzhVo : successList) {//用来编辑校验规则等
                JgzhData jgzhData = new JgzhData();
                BeanUtils.copyProperties(jgzhVo, jgzhData);
                jgzhData.setId(IdUtil.simpleUUID());
                jgzhData.setCreateTime(dt);
                jgzhData.setCreateBy(userName);
                jgzhData.setUpdateTime(dt);
                jgzhData.setUpdateBy(userName);
                //判断金额是不是数字
                jgzhDataList.add(jgzhData);
            }
        } else {
            xzpOpLog.setDealRes("失败");
            xzpOpLog.setOpinion("全量导入监管账户被执行反馈信息失败:无数据");
            xzpOpLog.setStepId("全量导入");
            xzpOpLog.setStepName("全量导入监管账户被执行反馈信息");
            xzpOpLog.setDealUser(userName);
            xzpOpLog.setCreateBy(userName);
            xzpOpLog.setStartTime(dt);
            xzpOpLog.setCreateTime(dt);
            xzpOpLog.setApplyType("jgzh");
            xzpOpLog.setFlowId("xzp_jgzh_info");
            xzpOpLogService.addLog(xzpOpLog);
            throw new BizException("导入失败，无数据");
        }
        // 保存数据到数据库
        try {
            //全量覆盖数据，先清理后保存
            QueryWrapper qw = new QueryWrapper();
            this.remove(qw);
            log.info(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())+" :成功清除旧数据,批量保存新数据>>>>start:");
            long t3 = System.currentTimeMillis();
            this.saveBatch(jgzhDataList);
            long t4 = System.currentTimeMillis();
            log.info(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())+" :end,全量入库完成,入库耗时 "+ (t4-t3)+" 秒；总耗时 "+ (t4-t1)+" 毫秒");
            xzpOpLog.setDealRes("成功");
            xzpOpLog.setOpinion("全量导入监管账户被执行反馈信息成功");
        } catch (Exception e) {
            log.error("监管账户被执行信息信息导入失败", e);
            xzpOpLog.setDealRes("失败");
            xzpOpLog.setOpinion("全量导入监管账户被执行反馈信息失败:"+e);
            throw new BizException("导入失败，请核对模板数据是否正确");
        }finally {
            xzpOpLog.setStepId("全量导入");
            xzpOpLog.setStepName("全量导入监管账户被执行反馈信息");
            xzpOpLog.setDealUser(userName);
            xzpOpLog.setCreateBy(userName);
            xzpOpLog.setStartTime(dt);
            xzpOpLog.setCreateTime(dt);
            xzpOpLog.setApplyType("jgzh");
            xzpOpLog.setFlowId("xzp_jgzh_info");
            xzpOpLogService.addLog(xzpOpLog);
        }
    }

    /**
     * 增量导入监管账户被执行信息数据
     * */
    @Override
    public void importJgzhAdd(MultipartFile file) throws Exception {
        //记录日志
        XzpOpLog xzpOpLog = new XzpOpLog();
        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值
        try{
            userName = UserInfoContext.currentUserName();
        }catch(Exception e){
            log.error("[importJgzhAdd]获取用户名异常，"+e);
        }
       // log.info("当前登录人>>>>"+userName);

        ImportParams params = new ImportParams();
        // 设置表格标题行数
        params.setTitleRows(2);//前2行，说明
        // 设置标题行数
        params.setHeadRows(1);//标题
        // 开启excel校验 注解校验
        params.setNeedVerify(true);
        // 导入模板信息
        //ExcelImportResult<ExcelJgzhVo> result = ExcelImportUtil.importExcelMore(file.getInputStream(), ExcelJgzhVo.class, params);
        long t1 = System.currentTimeMillis();
        List<ExcelJgzhVO> successList = ExcelImportUtil.importExcel(file.getInputStream(), ExcelJgzhVO.class, params);
        long t2= System.currentTimeMillis();
        log.info("解析监管账户被执行信息导入excel，获取到数据条数>>>>"+successList.size());
        log.info("解析监管账户被执行信息导入excel，耗时约>>>>"+(t2-t1)+"毫秒");
        //新增记录
        List<JgzhData> jgzhDataList = new ArrayList<>();
        //修改记录
        List<JgzhData> jgzhUptList = new ArrayList<>();
        //不可修改记录
        List<HashMap> editDisabeldList = new ArrayList<>();
        if (!successList.isEmpty()) {
            for (ExcelJgzhVO jgzhVo : successList) {//用来编辑校验规则等
                JgzhData jgzhData = new JgzhData();
                BeanUtils.copyProperties(jgzhVo, jgzhData);
                String serialno = jgzhData.getSerialno();
                if(StringUtils.isEmpty(serialno)){
                    throw new BizException("导入失败！serialno =【"+serialno+"】流水号必填！");
                }
                String bankid = jgzhData.getBankid();
                if(StringUtils.isEmpty(bankid)){
                    throw new BizException("导入失败！serialno =【"+serialno+"】,bankid =【"+bankid+"】开户银行代码必填且为数字类型！");
                }
                if(!isLong(bankid)) {
                    throw new BizException("导入失败！serialno =【"+serialno+"】,bankid =【"+bankid+"】开户银行代码不是整型，请调整！");
                }
                String accountname = jgzhData.getAccountname();
                if(StringUtils.isEmpty(accountname)){
                    throw new BizException("导入失败！serialno =【"+serialno+"】,accountname =【"+accountname+"】账户名必填！");
                }
                String accountno = jgzhData.getAccountno();
                if(StringUtils.isEmpty(accountno)){
                    throw new BizException("导入失败！serialno =【"+serialno+"】,accountno =【"+accountno+"】账户号必填！");
                }
                String executetype = jgzhData.getExecutetype();
                if(StringUtils.isEmpty(executetype)){
                    throw new BizException("导入失败！serialno =【"+serialno+"】,executetype =【"+executetype+"】执行类型必填！");
                }
                //判断数据executeamount 是否数字类型,非必填
                String executeamount = jgzhData.getExecuteamount();
                if(!StringUtils.isEmpty(executeamount) && executeamount != null){
                    if(!isNumeric(executeamount)){
                        throw new BizException("导入失败！【"+serialno+"】的金额【" + executeamount + "】不是数字类型，请调整！");
                    }
                }
                String executeDate = jgzhData.getExecutedate();
                if(!StringUtils.isEmpty(executeDate)){
                    if(!isValidFormat(executeDate)){
                        throw new BizException("导入失败！serialno =【"+serialno+"】,executedate =【"+executeDate+"】执行时间格式不对，必须为【yyyy-mm-dd hh24:mi:ss】格式！");
                    }
                }
                String executedept = jgzhData.getExecutedept();
                if(StringUtils.isEmpty(executedept)){
                    throw new BizException("导入失败！serialno =【"+serialno+"】,executedept =【"+executedept+"】执行部门必填！");
                }
                String releaseserialno = jgzhData.getReleaseserialno();
                String releasetime = jgzhData.getReleasetime();
                if ("10".equals(executetype)){ //解除执行的情况下必填
                    if(StringUtils.isEmpty(releaseserialno)){
                        throw new BizException("导入失败！serialno =【"+serialno+"】执行类型为10-解除执行的时候,releaseserialno =【"+releaseserialno+"】解除执行原流水号必填！");
                    }
                    if(StringUtils.isEmpty(releasetime)){
                        throw new BizException("导入失败！serialno =【"+serialno+"】执行类型为10-解除执行的时候,releasetime =【"+releasetime+"】解除执行时间必填！");
                    }
                }
                if(!StringUtils.isEmpty(releasetime)){
                    if(!isValidFormat(releasetime)){
                        throw new BizException("导入失败！serialno =【"+serialno+"】,releasetime =【"+releasetime+"】解除执行时间格式不对，必须为【yyyy-mm-dd hh24:mi:ss】格式！");
                    }
                }
                if ("90".equals(executetype)) { //执行类型为其他时必填备注信息
                    String note = jgzhData.getNote();
                    if(StringUtils.isEmpty(note)){
                        throw new BizException("导入失败！serialno =【"+serialno+"】执行类型为90-其他的时候,note =【"+note+"】备注必填！");
                    }
                }
                //判断时间是否符合格式 yyyy-mm-dd hh24:mi:ss

                //判断是否存在一样的“流水号”，是则更新，否则新增
                List<JgzhData> list = null;
                if("".equals(serialno)||serialno==null){
                    throw new BizException("导入失败，流水号无数据！可能存在空行或其他格式问题，请检查！");
                }else{
                    LambdaQueryWrapper<JgzhData> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(JgzhData::getSerialno, jgzhData.getSerialno());
                    list = this.list(queryWrapper);
                }
                if(list!=null&&list.size()>0){//存在
                    //判断查询结果
                    for (JgzhData jd:list){//同一个流水号只有1条
                        if(jd.getJgzhStatus()=="1"||jd.getJgzhStatus()=="3"){
                            log.error("存在记录已经审核通过或反馈成功，不再更新，请注意--执行流水号："+serialno);
                            HashMap disMap = new HashMap();
                            disMap.put("serialno",serialno);
                            editDisabeldList.add(disMap);
                        }else{
                            jgzhData.setId(jd.getId());
                            jgzhData.setUpdateTime(dt);
                            jgzhData.setUpdateBy(userName);
                            jgzhData.setDataSource("3");//导入
                            jgzhData.setImpBatch(dt);
                            jgzhUptList.add(jgzhData);
                        }
                    }
                }else{
                    jgzhData.setId(IdUtil.simpleUUID());
                    jgzhData.setCreateTime(dt);
                    jgzhData.setCreateBy(userName);
                    jgzhData.setUpdateTime(dt);
                    jgzhData.setUpdateBy(userName);
                    jgzhData.setDataSource("3");//导入
                    jgzhData.setImpBatch(dt);
                    jgzhDataList.add(jgzhData);
                }

            }
        } else {
            xzpOpLog.setDealRes("失败");
            xzpOpLog.setOpinion("增量导入监管账户被执行反馈信息失败:无数据");
            xzpOpLog.setStepId("导入");
            xzpOpLog.setStepName("增量导入监管账户被执行反馈信息");
            xzpOpLog.setDealUser(userName);
            xzpOpLog.setCreateBy(userName);
            xzpOpLog.setStartTime(dt);
            xzpOpLog.setCreateTime(dt);
            xzpOpLog.setApplyType("jgzh");
            xzpOpLog.setFlowId("xzp_jgzh_info");
            xzpOpLogService.addLog(xzpOpLog);
            throw new BizException("导入监管账户被执行反馈信息失败:无数据");
        }
        log.info("总共处理"+successList.size()+"条数据，其中"+jgzhDataList.size()+"条新增，"+jgzhUptList.size()+"条修改，"+editDisabeldList.size()+"条不可修改");
        // 保存数据到数据库
        try {
            //增量增加数据
//            QueryWrapper qw = new QueryWrapper();
//            this.remove(qw);
//            log.info("成功清除旧数据,批量保存新数据>>>>start:"+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            long t3 = System.currentTimeMillis();
            if(jgzhDataList.size()>0){
                this.saveBatch(jgzhDataList);
                //导入成功，提交审核
                jgzhSpDataMapper.insertJgzhSpDataBatch(jgzhDataList);
            }
            //根据serialno批量更新
            // 待处理疑问：关于处于已审核通过/反馈成功的是否也要被更新重新审核？当前这2种状态信息不会被更新
           if(jgzhUptList.size()>0){
               int uptnum = jgzhDataMapper.updateJgzhBatchByParam(jgzhUptList);
               log.info("增量导入监管账户被执行反馈信息，其中更新"+uptnum+"条记录");
               long t4 = System.currentTimeMillis();
               log.info(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())+":增量入库完成,耗时约 "+ (t4-t3)+" 毫秒；总耗时 "+ (t4-t1)+" 毫秒");
               //更新后的也要提交审核
               jgzhSpDataMapper.insertJgzhSpDataBatch(jgzhUptList);
           }
            xzpOpLog.setDealRes("成功");
            xzpOpLog.setOpinion("增量导入监管账户被执行反馈信息成功，" +
                    "总共处理"+successList.size()+"条数据，其中"+jgzhDataList.size()+"条新增，"+jgzhUptList.size()+"条修改，"+editDisabeldList.size()+"条已审核通过不可修改");
        } catch (Exception e) {
            log.error("监管账户被执行信息信息增量导入失败", e);
            xzpOpLog.setDealRes("失败");
            xzpOpLog.setOpinion("增量导入监管账户被执行反馈信息失败:"+e);
            xzpOpLog.setStepId("导入");
            xzpOpLog.setStepName("增量导入监管账户被执行反馈信息");
            xzpOpLog.setDealUser(userName);
            xzpOpLog.setCreateBy(userName);
            xzpOpLog.setStartTime(dt);
            xzpOpLog.setCreateTime(dt);
            xzpOpLog.setApplyType("jgzh");
            xzpOpLog.setFlowId("xzp_jgzh_info");
            xzpOpLogService.addLog(xzpOpLog);
            throw new BizException("导入失败，请核对模板数据是否正确");
        }finally {
            xzpOpLog.setStepId("导入");
            xzpOpLog.setStepName("增量导入监管账户被执行反馈信息");
            xzpOpLog.setDealUser(userName);
            xzpOpLog.setCreateBy(userName);
            xzpOpLog.setStartTime(dt);
            xzpOpLog.setCreateTime(dt);
            xzpOpLog.setApplyType("jgzh");
            xzpOpLog.setFlowId("xzp_jgzh_info");
            xzpOpLogService.addLog(xzpOpLog);
        }
    }

    /**
     * 修改监管账户信息
     * */
    @Override
    public boolean editJgzh(JgzhData jgzhData) {
       // log.info("更新监管账户被执行信息:[{}]", jgzhData.toString());
        log.info("更新监管账户被执行信息:[{}]", "");
        //记录日志
        XzpOpLog xzpOpLog = new XzpOpLog();

        JgzhData dd = jgzhData;
        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值
        try{
            userName = UserInfoContext.currentUserName();
        }catch(Exception e){
            log.error("[editJgzh]获取用户名异常，"+e);
        }
        dd.setUpdateTime(dt);//更新时间
        dd.setUpdateBy(userName);//更新人
        try{

            String jgzhStatus = jgzhData.getJgzhStatus();//查看状态
            if("2".equals(jgzhStatus))//审核不通过
            {
                dd.setJgzhStatus("0");//重新待审核
                jgzhDataMapper.updateJgzhById(dd);
                xzpOpLog.setDealRes("成功");
                xzpOpLog.setOpinion("修改监管账户被执行反馈信息成功");
                //修改完成后需要新增审核记录
                jgzhSpDataMapper.insertJgzhSpData(jgzhData);
            }else{
                jgzhDataMapper.updateJgzhById(dd);
                xzpOpLog.setDealRes("成功");
                xzpOpLog.setOpinion("修改监管账户被执行反馈信息成功");
            }
        }catch(Exception e){
            log.error("修改监管账户被执行信息异常！"+e);
            xzpOpLog.setDealRes("失败");
            xzpOpLog.setOpinion("修改监管账户被执行反馈信息失败");
        }finally {
            xzpOpLog.setStepId("修改");
            xzpOpLog.setStepName("修改监管账户被执行反馈信息");
            xzpOpLog.setDealUser(userName);
            xzpOpLog.setCreateBy(userName);
            xzpOpLog.setStartTime(dt);
            xzpOpLog.setCreateTime(dt);
            xzpOpLog.setApplyType("jgzh");
            xzpOpLog.setFlowId("xzp_jgzh_info");
            xzpOpLogService.addLog(xzpOpLog);
        }
        return true;
    }

    /**
     * 查询监管账户被执行信息
     * */
    @Override
    public JgzhData getJgzh(String id) {
        log.info("查询监管账户被执行信息项目:[id:{}]", id);
        JgzhData jgzhData = jgzhDataMapper.selectInfoById(id);
        //记录日志
        XzpOpLog xzpOpLog = new XzpOpLog();
        xzpOpLog.setStepId("查询");
        xzpOpLog.setStepName("查询监管账户被执行反馈信息详情");
        xzpOpLog.setDealRes("成功");
        xzpOpLog.setOpinion("查询监管账户被执行反馈信息详情成功："+id);
        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值
        try{
            userName = UserInfoContext.currentUserName();
        }catch(Exception e){
            log.error("[getJgzh]获取用户名异常，"+e);
        }
        xzpOpLog.setDealUser(userName);
        xzpOpLog.setCreateBy(userName);
        xzpOpLog.setStartTime(dt);
        xzpOpLog.setCreateTime(dt);
        xzpOpLog.setApplyType("jgzh");
        xzpOpLog.setFlowId("xzp_jgzh_info");
        xzpOpLogService.addLog(xzpOpLog);
        return jgzhData;
    }



    /**
     * 批量删除监管账户被执行信息记录
     * */
    @Override
    public boolean removeBatchJgzh(List<String> ids) {
        log.info("批量删除监管账户被执行信息记录:[ids:{}]", ids);
        int count = jgzhDataMapper.deleteBatchIds(ids);
        //记录日志
        XzpOpLog xzpOpLog = new XzpOpLog();
        xzpOpLog.setStepId("删除");
        xzpOpLog.setStepName("批量删除监管账户被执行反馈信息记录");
        xzpOpLog.setDealRes("成功");
        xzpOpLog.setOpinion("批量删除监管账户被执行反馈信息记录成功："+count+"条，id列表：["+ids+"]");
        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值
        try{
            userName = UserInfoContext.currentUserName();
        }catch(Exception e){
            log.error("[removeBatchJgzh]获取用户名异常，"+e);
        }
        xzpOpLog.setDealUser(userName);
        xzpOpLog.setCreateBy(userName);
        xzpOpLog.setStartTime(dt);
        xzpOpLog.setCreateTime(dt);
        xzpOpLog.setApplyType("jgzh");
        xzpOpLog.setFlowId("xzp_jgzh_info");
        xzpOpLogService.addLog(xzpOpLog);
        return count > 0;
    }

    /**
     * 删除监管账户被执行信息记录
     * */
    @Override
    public boolean removeJgzh(String id) {
        log.info("删除一条监管账户被执行信息记录:[id:{}]", id);
        //根据申请id删除审核记录
        int spCount = jgzhSpDataMapper.deleteByApplyId(id);
        // 根据id删除功能
        int count = jgzhDataMapper.deleteById(id);
        //记录日志
        XzpOpLog xzpOpLog = new XzpOpLog();
        xzpOpLog.setStepId("删除");
        xzpOpLog.setStepName("删除监管账户被执行反馈信息记录");
        xzpOpLog.setDealRes("成功");
        xzpOpLog.setOpinion("删除监管账户被执行反馈信息记录成功："+count+"条，id：["+id+"]");
        String dt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String userName ="default";//异常情况下工号使用默认值
        try{
            userName = UserInfoContext.currentUserName();
        }catch(Exception e){
            log.error("[removeJgzh]获取用户名异常，"+e);
        }
        xzpOpLog.setDealUser(userName);
        xzpOpLog.setCreateBy(userName);
        xzpOpLog.setStartTime(dt);
        xzpOpLog.setCreateTime(dt);
        xzpOpLog.setApplyType("jgzh");
        xzpOpLog.setFlowId("xzp_jgzh_info");
        xzpOpLogService.addLog(xzpOpLog);
        return count > 0;
    }
    

    /**
     * 监管账户条件构造工具
     *
     * @param jgzhData 查询条件
     * @return LambdaQueryWrapper<JgzhData>
     */
    public LambdaQueryWrapper<JgzhData> getJgzhQueryWrapper(JgzhData jgzhData) {

        LambdaQueryWrapper<JgzhData> queryWrapper = new LambdaQueryWrapper<>();
        //执行流水号查询
        if (jgzhData.getSerialno() != null && !jgzhData.getSerialno().isEmpty()) {
            queryWrapper.like(JgzhData::getSerialno, jgzhData.getSerialno());
        }
        queryWrapper.orderByDesc(JgzhData::getSerialno, JgzhData::getExecutedate);
        return queryWrapper;

    }

    /**
     * 判断字符串是否数字类型
     * */
    public static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        try {
            Double.parseDouble(str);  // 尝试转为 double
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    /**
     * 判断字符串是否整型
     * */
    public static boolean isLong(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        try {
            Long.parseLong(str);  // 尝试转为 long
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断是否符合日期格式
     * */
    public static boolean isValidFormat(String dateTimeStr) {
        if (dateTimeStr == null) {
            return false;
        }
        String regex = "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])\\s([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$";
        return Pattern.matches(regex, dateTimeStr);
    }

}

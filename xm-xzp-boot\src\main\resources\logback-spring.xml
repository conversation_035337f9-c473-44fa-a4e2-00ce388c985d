<?xml version="1.0" encoding="UTF-8"?>
<!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 -->
<!-- scan:当此属性设置为true时，配置文档如果发生改变，将会被重新加载，默认值为true -->
<!-- scanPeriod:设置监测配置文档是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。
                 当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<configuration  scan="true" scanPeriod="10 seconds">
    <!-- 设置上下文名称 -->
    <contextName>logback</contextName>

    <!-- name的值是变量的名称，value的值时变量定义的值。通过定义的值会被插入到logger上下文中。定义后，可以使“${}”来使用变量。-->
    <!-- 应用名称 -->
    <property name="app_name" value="yoaf" />
    <!-- 日志目录 -->
    <property name="log_dir" value="./logs/" />
    <!-- 定义日志存放的位置及文件名属性 -->
    <property name="log_file" value="${log_dir}/${app_name}-log.log" />
    <property name="log_file_warn" value="${log_dir}/${app_name}-log-warn.log" />
    <property name="log_file_error" value="${log_dir}/${app_name}-log-error.log" />
    <property name="log_file_sql" value="${log_dir}/${app_name}-log-sql.log" />
    <!-- SQL日志级别 -->
    <property name="log_level_sql" value="DEBUG" />
    <!-- 历史日志保留天数 -->
    <property name="log.maxHistory" value="15" />
    <!-- 日志文件最大大小 -->
    <property name="log.maxFileSize" value="100MB" />


    <!-- 日志格式和颜色渲染 -->
    <!-- 彩色日志依赖的渲染 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />

    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <!-- 控制台日志配置 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <Pattern>${CONSOLE_LOG_PATTERN}</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 默认日志文件配置 -->
    <appender name="ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log_file}</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_file}.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>${log.maxFileSize}</maxFileSize>
            <maxHistory>${log.maxHistory}</maxHistory>
            <totalSizeCap>0</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- SQL日志文件配置 -->
    <appender name="SQL_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log_file_sql}</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_file_sql}.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxFileSize>${log.maxFileSize}</maxFileSize>
            <maxHistory>${log.maxHistory}</maxHistory>
            <totalSizeCap>0</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!--
      <logger>用来设置某一个包或者具体的某一个类的日志打印级别、
      以及指定<appender>。<logger>仅有一个name属性，
      一个可选的level和一个可选的additivity属性。
      name:用来指定受此logger约束的某一个包或者具体的某一个类。
      level:用来设置打印级别，大小写无关：TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，
            还有一个特俗值INHERITED或者同义词NULL，代表强制执行上级的级别,如果未设置此属性，那么当前logger将会继承上级的级别。
      additivity:是否向上级logger传递打印信息。默认是true。
  -->
    <!-- 后台系统管理SQL日志 -->
    <logger name="com.psbc.pfpj.yoaf.admin.mapper" additivity="true" level="${log_level_sql}">
        <appender-ref ref="SQL_FILE"/>
    </logger>
    <!-- 审计日志SQL日志 -->
    <logger name="com.psbc.pfpj.yoaf.auditlog.mapper" additivity="true" level="${log_level_sql}">
        <appender-ref ref="SQL_FILE"/>
    </logger>
    <!-- 认证中心SQL日志 -->
    <logger name="com.psbc.pfpj.yoaf.auth.center.mapper" additivity="true" level="${log_level_sql}">
        <appender-ref ref="SQL_FILE"/>
    </logger>
    <!-- 业务字典SQL日志 -->
    <logger name="com.psbc.pfpj.yoaf.dictionary.mapper" additivity="true" level="${log_level_sql}">
        <appender-ref ref="SQL_FILE"/>
    </logger>

    <!--
      root节点是必选节点，用来指定最基础的日志输出级别，只有一个level属性
      level:用来设置打印级别，大小写无关：TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，
      不能设置为INHERITED或者同义词NULL。默认是DEBUG
      可以包含零个或多个元素，标识这个appender将会添加到这个logger
  -->
    <!-- 开发环境:打印控制台-->
    <springProfile name="dev,uaas,ldap">
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ROLLING_FILE" />
        </root>
    </springProfile>

    <!-- 测试环境/生产环境:输出到文档 -->
    <springProfile name="dev,test,prod">
        <!-- WARN级别日志文件配置 -->
        <appender name="WARN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${log_file_warn}</file>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
                <charset>UTF-8</charset>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${log_file_warn}.%d{yyyy-MM-dd}.%i</fileNamePattern>
                <maxFileSize>${log.maxFileSize}</maxFileSize>
                <maxHistory>${log.maxHistory}</maxHistory>
                <totalSizeCap>0</totalSizeCap>
            </rollingPolicy>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>WARN</level>
                <onMatch>ACCEPT</onMatch>
                <onMismatch>DENY</onMismatch>
            </filter>
        </appender>
        <!-- ERROR级别日志文件配置 -->
        <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${log_file_error}</file>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
                <charset>UTF-8</charset>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${log_file_error}.%d{yyyy-MM-dd}.%i</fileNamePattern>
                <maxFileSize>${log.maxFileSize}</maxFileSize>
                <maxHistory>${log.maxHistory}</maxHistory>
                <totalSizeCap>0</totalSizeCap>
            </rollingPolicy>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>ACCEPT</onMatch>
                <onMismatch>DENY</onMismatch>
            </filter>
        </appender>
        <root level="INFO">
            <appender-ref ref="ROLLING_FILE" />
            <appender-ref ref="WARN_FILE" />
            <appender-ref ref="ERROR_FILE" />
        </root>
    </springProfile>

</configuration>

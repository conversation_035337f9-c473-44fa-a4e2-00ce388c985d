<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xm-xzp</artifactId>
        <groupId>com.xm.xzp</groupId>
        <version>1.1.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>xm-xzp-api</artifactId>
    <name>xm-xzp-api</name>
    <description>厦门统一接入-新中平管理模块</description>

    <!-- 引入依赖 -->
    <dependencies>
        <!-- springboot-web模块[已提供依赖范围] -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- 公共-返回对象工具类 -->
        <dependency>
            <groupId>com.psbc.pfpj</groupId>
            <artifactId>yoaf-common-response-result</artifactId>
        </dependency>
        <!-- 公共-hibernate验证模块 -->
        <dependency>
            <groupId>com.psbc.pfpj</groupId>
            <artifactId>yoaf-common-hibernate-validator</artifactId>
        </dependency>
        <!-- mybatis-plus注解类包 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
        </dependency>
        <!-- swagger注解类包 -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <!-- pagehelper分页 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
            <version>3.3.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-annotation</artifactId>
            <version>4.2.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>


</project>
package com.xm.xzp.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * @version 1.0.0
 */
@ExcelTarget("dict")
@Data
public class ExcelJgzhVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 执行流水号
     */
    @ApiModelProperty(value = "执行流水号")
    @Excel(name = "执行流水号")
    private String serialno;

    /**
     * 开户银行代码
     */
    @ApiModelProperty(value = "开户银行代码")
    @Excel(name = "开户银行代码")
    private String bankid;

    /**
     * 账户名
     */
    @ApiModelProperty(value = "账户名")
    @Excel(name = "账户名")
    private String accountname;

    /**
     * 账户号
     */
    @ApiModelProperty(value = "账户号")
    @Excel(name = "账户号")
    private String accountno;

    /**
     * 执行类型
     */
    @ApiModelProperty(value = "执行类型")
    @Excel(name = "执行类型")
    private String executetype;

    /**
     * 执行金额
     */
    @ApiModelProperty(value = "执行金额")
    @Excel(name = "执行金额")
    private String executeamount;

    /**
     * 执行部门
     */
    @ApiModelProperty(value = "执行部门")
    @Excel(name = "执行部门")
    private String executedept;

    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    @Excel(name = "执行时间")
    private String executedate;

    /**
     * 解除执行原流水号
     */
    @ApiModelProperty(value = "解除执行原流水号")
    @Excel(name = "解除执行原流水号")
    private String releaseserialno;

    /**
     * 解除执行时间
     */
    @ApiModelProperty(value = "解除执行时间")
    @Excel(name = "解除执行时间")
    private String releasetime;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    @Excel(name = "备注说明")
    private String note;

}

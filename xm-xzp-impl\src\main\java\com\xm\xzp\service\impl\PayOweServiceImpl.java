package com.xm.xzp.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.mapper.PayOweMapper;
import com.xm.xzp.model.entity.PayOwe;
import com.xm.xzp.model.entity.PayOweVo;
import com.xm.xzp.service.IPayOweService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class PayOweServiceImpl extends ServiceImpl<PayOweMapper, PayOwe> implements IPayOweService {

    @Resource
    private PayOweMapper payOweMapper;

    @Override
    @DS("datasource2")
    public int batchUpdateTxnStaToZero(String opeCd, String merchId, String wlBatchId) {
        log.debug("执行批量更新交易状态，参数：opeCd={}, merchId={}, wlBatchId={}", opeCd, merchId, wlBatchId);
        return payOweMapper.updateTxnStaToZero(opeCd, merchId, wlBatchId);
    }

    @Override
    @DS("datasource2")
    public PageInfo<PayOwe> payOweList(PayOweVo payOweVo, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<PayOwe> list = this.list(payOweQueryWrapper(payOweVo));
        return new PageInfo<>(list);
    }

    /**
     * 构建查询条件
     *
     * @param payOweVo 查询条件对象
     * @return QueryWrapper
     */
    private QueryWrapper<PayOwe> payOweQueryWrapper(PayOweVo payOweVo) {
        QueryWrapper<PayOwe> queryWrapper = new QueryWrapper<>();
        if (payOweVo != null) {
            LambdaQueryWrapper<PayOwe> lambdaQueryWrapper = queryWrapper.lambda();

            // 时间区间查询
            if (StringUtils.isNotBlank(payOweVo.getStartTime())
                    && StringUtils.isNotBlank(payOweVo.getEndTime())) {
                lambdaQueryWrapper.between(PayOwe::getTranDt,
                        payOweVo.getStartTime(), payOweVo.getEndTime());
            }

            // 商户号查询
            if (StringUtils.isNotBlank(payOweVo.getMerchId())) {
                lambdaQueryWrapper.eq(PayOwe::getMerchId, payOweVo.getMerchId());
            }

            // 操作码查询
            if (StringUtils.isNotBlank(payOweVo.getOpeCd())) {
                lambdaQueryWrapper.eq(PayOwe::getOpeCd, payOweVo.getOpeCd());
            }
        }

        // 按交易日期降序排序
        queryWrapper.orderByDesc("tran_dt");
        return queryWrapper;
    }
}
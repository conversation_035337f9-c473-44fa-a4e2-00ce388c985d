package com.xm.xzp.model.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class BatchProcessLogVo {
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyyMMdd")
    private String startTime;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyyMMdd")
    private String endTime;
    /**
     * 操作码
     */
    private String opeCd;
    /**
     * 商户号
     */
    private String merchId;
    /**
     * 批次号
     */
    private String batchId;
}

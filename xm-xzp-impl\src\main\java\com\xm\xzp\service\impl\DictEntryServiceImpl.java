package com.xm.xzp.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xm.xzp.mapper.DictEntryMapper;
import com.xm.xzp.model.entity.DictEntry;
import com.xm.xzp.model.query.DictEntryQuery;
import com.xm.xzp.service.IDictEntryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class DictEntryServiceImpl extends ServiceImpl<DictEntryMapper, DictEntry> implements IDictEntryService {

    @Resource
    private DictEntryMapper dictEntryMapper;
    @Override
    @DS("datasource1")
    public List<DictEntry> selectDictEntry(DictEntryQuery dictEntryQuery) {
        List<DictEntry> list = this.list(selectDictEntryQueryWrapper(dictEntryQuery));
        return list;
    }

    /**
     * 构建查询条件
     *
     * @param dictEntryQuery 查询条件对象
     * @return QueryWrapper
     */
    private QueryWrapper<DictEntry> selectDictEntryQueryWrapper(DictEntryQuery dictEntryQuery) {
        QueryWrapper<DictEntry> queryWrapper = new QueryWrapper<>();
        if (dictEntryQuery != null) {
            LambdaQueryWrapper<DictEntry> lambdaQueryWrapper = queryWrapper.lambda();
            //字典类型代码
            if ( StringUtils.isNotBlank(dictEntryQuery.getDictTypeId()) ) {
                lambdaQueryWrapper.eq(DictEntry::getDictTypeId, dictEntryQuery.getDictTypeId());
            }
            //字典项代码
            if (StringUtils.isNotBlank(dictEntryQuery.getDictId())) {
                lambdaQueryWrapper.eq(DictEntry::getDictId, dictEntryQuery.getDictId());
            }
            //字典项名称
            if (StringUtils.isNotBlank(dictEntryQuery.getDictName())) {
                lambdaQueryWrapper.eq(DictEntry::getDictName, dictEntryQuery.getDictName());
            }
            //状态
            if (StringUtils.isNotBlank(dictEntryQuery.getStatus())) {
                lambdaQueryWrapper.eq(DictEntry::getStatus, dictEntryQuery.getStatus());
            }
        }
        queryWrapper.orderByAsc("dict_id");
        return queryWrapper;
    }
}

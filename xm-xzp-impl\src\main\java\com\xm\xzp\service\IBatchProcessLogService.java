package com.xm.xzp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.xm.xzp.model.entity.BatchProcessLog;
import com.xm.xzp.model.vo.BatchProcessLogVo;

public interface IBatchProcessLogService extends IService<BatchProcessLog> {

    /**
     * 分页查询批量处理日志
     *
     * @param batchProcessLog 查询条件
     * @param pageNum 当前页
     * @param pageSize 每页数量
     * @return 分页结果
     */
    PageInfo<BatchProcessLog> batchProcessLogList(BatchProcessLogVo batchProcessLog, Integer pageNum, Integer pageSize);
}